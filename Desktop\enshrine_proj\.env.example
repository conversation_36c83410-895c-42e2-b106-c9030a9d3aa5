# Google Cloud / Gemini API Configuration
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_CLOUD_PROJECT=your_project_id_here

# OpenWeatherMap API (free tier available)
OPENWEATHER_API_KEY=your_openweather_api_key_here

# NewsAPI (free tier available)
NEWS_API_KEY=your_news_api_key_here

# Optional: Additional APIs
COINGECKO_API_KEY=your_coingecko_api_key_here

# Application Settings
LOG_LEVEL=INFO
MAX_ITERATIONS=3
TIMEOUT_SECONDS=30
