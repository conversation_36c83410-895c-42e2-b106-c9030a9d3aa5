"""NewsAPI integration tool."""

import asyncio
import httpx
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from config.settings import settings


class NewsAPI:
    """NewsAPI client for fetching news articles."""
    
    def __init__(self):
        self.base_url = settings.NEWS_API_URL
        self.api_key = settings.NEWS_API_KEY
        self.timeout = settings.TIMEOUT_SECONDS
    
    async def search_everything(self, query: str, language: str = "en", 
                               sort_by: str = "relevancy", page_size: int = 10) -> Optional[Dict[str, Any]]:
        """Search for news articles using the everything endpoint."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                params = {
                    "q": query,
                    "language": language,
                    "sortBy": sort_by,
                    "pageSize": page_size,
                    "apiKey": self.api_key
                }
                response = await client.get(f"{self.base_url}/everything", params=params)
                response.raise_for_status()
                return response.json()
        except Exception as e:
            print(f"Error searching news: {e}")
            return None
    
    async def get_top_headlines(self, country: str = "us", category: str = "", 
                               page_size: int = 10) -> Optional[Dict[str, Any]]:
        """Get top headlines from NewsAPI."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                params = {
                    "country": country,
                    "pageSize": page_size,
                    "apiKey": self.api_key
                }
                if category:
                    params["category"] = category
                
                response = await client.get(f"{self.base_url}/top-headlines", params=params)
                response.raise_for_status()
                return response.json()
        except Exception as e:
            print(f"Error fetching top headlines: {e}")
            return None
    
    async def search_spacex_news(self, mission_name: str = "", days_back: int = 7) -> Optional[Dict[str, Any]]:
        """Search for SpaceX-related news."""
        # Build search query
        query_parts = ["SpaceX"]
        if mission_name:
            query_parts.append(mission_name)
        
        query = " ".join(query_parts)
        
        # Calculate date range
        from_date = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                params = {
                    "q": query,
                    "from": from_date,
                    "language": "en",
                    "sortBy": "relevancy",
                    "pageSize": 20,
                    "apiKey": self.api_key
                }
                response = await client.get(f"{self.base_url}/everything", params=params)
                response.raise_for_status()
                return response.json()
        except Exception as e:
            print(f"Error searching SpaceX news: {e}")
            return None
    
    def format_news_data(self, news_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format news data for easier consumption by agents."""
        if not news_data:
            return {}
        
        articles = []
        for article in news_data.get("articles", []):
            formatted_article = {
                "title": article.get("title", ""),
                "description": article.get("description", ""),
                "content": article.get("content", ""),
                "url": article.get("url", ""),
                "published_at": article.get("publishedAt", ""),
                "source": {
                    "name": article.get("source", {}).get("name", ""),
                    "id": article.get("source", {}).get("id", "")
                },
                "author": article.get("author", ""),
                "url_to_image": article.get("urlToImage", "")
            }
            
            # Parse publication date
            if formatted_article["published_at"]:
                try:
                    dt = datetime.fromisoformat(formatted_article["published_at"].replace('Z', '+00:00'))
                    formatted_article["published_timestamp"] = dt.timestamp()
                    formatted_article["published_readable"] = dt.strftime("%Y-%m-%d %H:%M:%S UTC")
                except:
                    formatted_article["published_timestamp"] = None
                    formatted_article["published_readable"] = formatted_article["published_at"]
            
            articles.append(formatted_article)
        
        formatted = {
            "total_results": news_data.get("totalResults", 0),
            "status": news_data.get("status", ""),
            "articles": articles
        }
        
        return formatted
    
    def analyze_news_sentiment(self, news_data: Dict[str, Any], mission_name: str = "") -> Dict[str, Any]:
        """Analyze news sentiment and extract relevant information."""
        if not news_data or not news_data.get("articles"):
            return {"sentiment": "neutral", "relevant_articles": 0, "summary": "No relevant news found"}
        
        articles = news_data["articles"]
        relevant_articles = []
        
        # Keywords that might indicate delays or issues
        negative_keywords = [
            "delay", "delayed", "postpone", "postponed", "abort", "aborted", 
            "cancel", "cancelled", "scrub", "scrubbed", "weather", "technical",
            "issue", "problem", "failure", "malfunction"
        ]
        
        # Keywords that indicate positive progress
        positive_keywords = [
            "successful", "success", "ready", "go", "launch", "countdown",
            "nominal", "on schedule", "green light", "approved"
        ]
        
        negative_count = 0
        positive_count = 0
        
        for article in articles:
            title = article.get("title", "").lower()
            description = article.get("description", "").lower()
            content = title + " " + description
            
            # Check if article is relevant to the mission
            is_relevant = False
            if mission_name:
                is_relevant = mission_name.lower() in content
            else:
                is_relevant = "spacex" in content or "falcon" in content or "dragon" in content
            
            if is_relevant:
                relevant_articles.append(article)
                
                # Count sentiment indicators
                for keyword in negative_keywords:
                    if keyword in content:
                        negative_count += 1
                        break
                
                for keyword in positive_keywords:
                    if keyword in content:
                        positive_count += 1
                        break
        
        # Determine overall sentiment
        if negative_count > positive_count:
            sentiment = "negative"
        elif positive_count > negative_count:
            sentiment = "positive"
        else:
            sentiment = "neutral"
        
        # Generate summary
        summary_parts = []
        if relevant_articles:
            summary_parts.append(f"Found {len(relevant_articles)} relevant articles")
            if negative_count > 0:
                summary_parts.append(f"{negative_count} articles mention potential issues")
            if positive_count > 0:
                summary_parts.append(f"{positive_count} articles are positive")
        else:
            summary_parts.append("No relevant news articles found")
        
        return {
            "sentiment": sentiment,
            "relevant_articles": len(relevant_articles),
            "negative_indicators": negative_count,
            "positive_indicators": positive_count,
            "summary": ". ".join(summary_parts),
            "top_articles": relevant_articles[:5]  # Top 5 most relevant
        }


# Function tools for ADK integration
async def search_spacex_mission_news(mission_name: str = "", days_back: int = 7) -> str:
    """Search for news related to SpaceX missions."""
    api = NewsAPI()
    news_data = await api.search_spacex_news(mission_name, days_back)
    
    if not news_data:
        return "Failed to fetch news data"
    
    formatted_data = api.format_news_data(news_data)
    sentiment_analysis = api.analyze_news_sentiment(formatted_data, mission_name)
    
    result = {
        "news": formatted_data,
        "analysis": sentiment_analysis
    }
    
    return str(result)


async def search_general_news(query: str, page_size: int = 10) -> str:
    """Search for general news articles."""
    api = NewsAPI()
    news_data = await api.search_everything(query, page_size=page_size)
    
    if not news_data:
        return "Failed to fetch news data"
    
    formatted_data = api.format_news_data(news_data)
    return str(formatted_data)
