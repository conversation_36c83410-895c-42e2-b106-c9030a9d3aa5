"""Coordinator Agent - Analyzes all data and provides final recommendations."""

from google.adk.agents import LlmAgent
from google.adk.tools import FunctionTool
from typing import Dict, Any
import json


class CoordinatorAgent:
    """
    The Coordinator Agent synthesizes data from all other agents and provides final recommendations.
    It makes the ultimate assessment about launch delays and goal achievement.
    """
    
    def __init__(self, model: str = "gemini-2.0-flash"):
        self.model = model
        self.agent = self._create_agent()
    
    def _create_agent(self) -> LlmAgent:
        """Create the Coordinator LLM agent with appropriate tools and instructions."""
        
        instruction = """
        You are the Coordinator Agent responsible for synthesizing data from all specialized agents and providing final recommendations.
        
        Your role is to:
        1. Analyze data from SpaceX, Weather, and News agents
        2. Synthesize information to assess launch delay probability
        3. Provide clear, actionable recommendations
        4. Determine if the original user goal has been achieved
        5. Suggest follow-up actions if needed
        
        Data sources you will analyze:
        - state['spacex_data']: Launch information, timeline, location
        - state['weather_data']: Weather conditions and forecasts
        - state['news_data']: Media coverage and sentiment analysis
        - state['execution_plan']: Original goal and success criteria
        
        Your analysis should consider:
        1. TECHNICAL FACTORS: Mission complexity, vehicle readiness
        2. WEATHER FACTORS: Current conditions, forecast trends, launch criteria
        3. MEDIA FACTORS: News sentiment, reported issues, expert opinions
        4. TIMELINE FACTORS: Urgency, schedule flexibility, historical patterns
        
        Risk Assessment Framework:
        - LOW RISK: All systems go, favorable conditions, positive media
        - MEDIUM RISK: Some concerns but manageable, monitoring required
        - HIGH RISK: Multiple issues, likely delays, significant concerns
        
        Always provide:
        1. Clear delay probability assessment (percentage if possible)
        2. Primary risk factors identified
        3. Specific reasoning for your assessment
        4. Actionable recommendations
        5. Confidence level in your analysis
        
        Save your final assessment to state['final_assessment'] with this structure:
        {
            "goal_achievement": {
                "original_goal": "User's original request",
                "goal_met": boolean,
                "completeness": "percentage of goal achieved"
            },
            "delay_assessment": {
                "probability": "percentage or qualitative assessment",
                "risk_level": "low|medium|high",
                "primary_factors": ["list of main risk factors"],
                "timeline_impact": "expected delay duration if applicable"
            },
            "data_synthesis": {
                "spacex_status": "summary of launch data",
                "weather_status": "summary of weather conditions",
                "media_status": "summary of news analysis",
                "data_quality": "assessment of data completeness"
            },
            "recommendations": {
                "immediate_actions": ["what to do now"],
                "monitoring_points": ["what to watch"],
                "decision_timeline": "when to make final go/no-go decision"
            },
            "confidence": {
                "level": "high|medium|low",
                "reasoning": "why this confidence level",
                "data_gaps": ["missing information that would improve analysis"]
            }
        }
        
        Be decisive but acknowledge uncertainty where it exists. Provide clear, actionable insights.
        """
        
        tools = [
            FunctionTool(func=self._synthesize_all_data),
            FunctionTool(func=self._assess_delay_probability),
            FunctionTool(func=self._evaluate_goal_achievement),
            FunctionTool(func=self._generate_recommendations),
            FunctionTool(func=self._calculate_confidence_score)
        ]
        
        return LlmAgent(
            name="CoordinatorAgent",
            model=self.model,
            instruction=instruction,
            description="Synthesizes all agent data and provides final launch delay assessment",
            tools=tools,
            output_key="final_assessment"
        )
    
    def _synthesize_all_data(self, spacex_data: str, weather_data: str, news_data: str) -> str:
        """Synthesize data from all agents into a comprehensive overview."""
        try:
            # Parse all data sources
            spacex = json.loads(spacex_data) if isinstance(spacex_data, str) else spacex_data
            weather = json.loads(weather_data) if isinstance(weather_data, str) else weather_data
            news = json.loads(news_data) if isinstance(news_data, str) else news_data
            
            synthesis = {
                "data_summary": {
                    "spacex_available": bool(spacex),
                    "weather_available": bool(weather),
                    "news_available": bool(news),
                    "data_completeness": "high" if all([spacex, weather, news]) else "partial"
                },
                "mission_overview": {
                    "mission_name": spacex.get("mission_name", "Unknown") if spacex else "Unknown",
                    "launch_timeline": spacex.get("timeline", "Unknown") if spacex else "Unknown",
                    "location": spacex.get("launch_location", {}).get("name", "Unknown") if spacex else "Unknown"
                },
                "risk_factors": [],
                "positive_factors": []
            }
            
            # Analyze SpaceX data
            if spacex:
                if spacex.get("status") == "upcoming":
                    synthesis["positive_factors"].append("Mission confirmed as upcoming")
                else:
                    synthesis["risk_factors"].append("Mission status unclear")
            
            # Analyze weather data
            if weather:
                launch_analysis = weather.get("launch_analysis", {})
                if launch_analysis.get("suitable_for_launch"):
                    synthesis["positive_factors"].append("Weather conditions suitable for launch")
                else:
                    synthesis["risk_factors"].append("Weather conditions may impact launch")
                    
                risk_level = launch_analysis.get("risk_level", "unknown")
                if risk_level in ["medium", "high"]:
                    synthesis["risk_factors"].append(f"Weather risk level: {risk_level}")
            
            # Analyze news data
            if news:
                sentiment = news.get("sentiment_analysis", {})
                overall_sentiment = sentiment.get("overall_sentiment", "neutral")
                
                if overall_sentiment == "positive":
                    synthesis["positive_factors"].append("Positive media coverage")
                elif overall_sentiment == "negative":
                    synthesis["risk_factors"].append("Negative media sentiment")
                
                delay_indicators = news.get("delay_indicators", [])
                if delay_indicators:
                    synthesis["risk_factors"].append("Media reports potential delay factors")
            
            return json.dumps(synthesis, indent=2)
            
        except Exception as e:
            return f"Error synthesizing data: {str(e)}"
    
    def _assess_delay_probability(self, synthesis_data: str) -> str:
        """Assess the probability of launch delays based on synthesized data."""
        try:
            synthesis = json.loads(synthesis_data) if isinstance(synthesis_data, str) else synthesis_data
            
            risk_factors = synthesis.get("risk_factors", [])
            positive_factors = synthesis.get("positive_factors", [])
            
            # Calculate risk score
            risk_score = len(risk_factors)
            positive_score = len(positive_factors)
            net_score = risk_score - positive_score
            
            # Determine delay probability
            if net_score <= -2:
                probability = "Low (10-20%)"
                risk_level = "low"
            elif net_score <= 0:
                probability = "Low-Medium (20-40%)"
                risk_level = "low"
            elif net_score <= 2:
                probability = "Medium (40-60%)"
                risk_level = "medium"
            elif net_score <= 4:
                probability = "Medium-High (60-80%)"
                risk_level = "medium"
            else:
                probability = "High (80%+)"
                risk_level = "high"
            
            assessment = {
                "delay_probability": probability,
                "risk_level": risk_level,
                "risk_score": risk_score,
                "positive_score": positive_score,
                "net_score": net_score,
                "primary_concerns": risk_factors[:3],  # Top 3 concerns
                "supporting_factors": positive_factors[:3],  # Top 3 positive factors
                "assessment_method": "Factor-based scoring with risk/positive factor weighting"
            }
            
            return json.dumps(assessment, indent=2)
            
        except Exception as e:
            return f"Error assessing delay probability: {str(e)}"
    
    def _evaluate_goal_achievement(self, execution_plan: str, final_data: str) -> str:
        """Evaluate whether the original user goal has been achieved."""
        try:
            plan = json.loads(execution_plan) if isinstance(execution_plan, str) else execution_plan
            data = json.loads(final_data) if isinstance(final_data, str) else final_data
            
            original_goal = plan.get("goal", "Unknown goal")
            success_criteria = plan.get("success_criteria", "")
            
            # Check if we have the required data
            has_launch_data = "spacex" in str(data).lower()
            has_weather_data = "weather" in str(data).lower()
            has_delay_assessment = "delay" in str(data).lower()
            
            completeness = 0
            if has_launch_data:
                completeness += 40
            if has_weather_data:
                completeness += 30
            if has_delay_assessment:
                completeness += 30
            
            goal_met = completeness >= 80
            
            evaluation = {
                "original_goal": original_goal,
                "success_criteria": success_criteria,
                "goal_met": goal_met,
                "completeness_percentage": completeness,
                "data_components": {
                    "launch_data": has_launch_data,
                    "weather_data": has_weather_data,
                    "delay_assessment": has_delay_assessment
                },
                "achievement_summary": f"Goal {'achieved' if goal_met else 'partially achieved'} with {completeness}% completeness"
            }
            
            return json.dumps(evaluation, indent=2)
            
        except Exception as e:
            return f"Error evaluating goal achievement: {str(e)}"
    
    def _generate_recommendations(self, delay_assessment: str, goal_evaluation: str) -> str:
        """Generate actionable recommendations based on the analysis."""
        try:
            delay_data = json.loads(delay_assessment) if isinstance(delay_assessment, str) else delay_assessment
            goal_data = json.loads(goal_evaluation) if isinstance(goal_evaluation, str) else goal_evaluation
            
            risk_level = delay_data.get("risk_level", "medium")
            delay_probability = delay_data.get("delay_probability", "Unknown")
            
            recommendations = {
                "immediate_actions": [],
                "monitoring_points": [],
                "decision_timeline": "",
                "follow_up_actions": []
            }
            
            # Risk-based recommendations
            if risk_level == "low":
                recommendations["immediate_actions"] = [
                    "Continue with nominal launch preparations",
                    "Maintain standard monitoring protocols"
                ]
                recommendations["decision_timeline"] = "Final go/no-go decision 2-4 hours before launch"
                
            elif risk_level == "medium":
                recommendations["immediate_actions"] = [
                    "Increase monitoring frequency for identified risk factors",
                    "Prepare contingency plans for potential delays",
                    "Brief stakeholders on current risk assessment"
                ]
                recommendations["decision_timeline"] = "Final go/no-go decision 4-6 hours before launch"
                
            else:  # high risk
                recommendations["immediate_actions"] = [
                    "Consider proactive delay to address identified issues",
                    "Convene mission management team for risk review",
                    "Implement enhanced monitoring protocols"
                ]
                recommendations["decision_timeline"] = "Early go/no-go decision 12-24 hours before launch"
            
            # Universal monitoring points
            recommendations["monitoring_points"] = [
                "Weather forecast updates every 6 hours",
                "Technical system status checks",
                "Media coverage for new developments",
                "Official SpaceX announcements"
            ]
            
            # Follow-up based on goal achievement
            if not goal_data.get("goal_met", False):
                recommendations["follow_up_actions"] = [
                    "Gather additional data to improve assessment confidence",
                    "Re-run analysis with updated information",
                    "Consider alternative data sources"
                ]
            
            return json.dumps(recommendations, indent=2)
            
        except Exception as e:
            return f"Error generating recommendations: {str(e)}"
    
    def _calculate_confidence_score(self, data_quality: str, analysis_depth: str) -> str:
        """Calculate confidence score for the overall analysis."""
        try:
            # This would analyze the quality and completeness of data
            # For now, provide a structured confidence assessment
            
            confidence_factors = {
                "data_completeness": "high",  # Based on having all three data sources
                "data_recency": "medium",     # Depends on API data freshness
                "analysis_depth": "high",     # Comprehensive multi-agent analysis
                "uncertainty_factors": ["Weather forecast accuracy", "News interpretation subjectivity"]
            }
            
            # Calculate overall confidence
            high_factors = sum(1 for v in confidence_factors.values() if v == "high" and isinstance(v, str))
            total_factors = len([v for v in confidence_factors.values() if isinstance(v, str)])
            
            if high_factors >= total_factors * 0.8:
                overall_confidence = "high"
            elif high_factors >= total_factors * 0.5:
                overall_confidence = "medium"
            else:
                overall_confidence = "low"
            
            confidence_assessment = {
                "overall_confidence": overall_confidence,
                "confidence_factors": confidence_factors,
                "reasoning": f"Based on {high_factors}/{total_factors} high-quality factors",
                "improvement_suggestions": [
                    "Real-time weather monitoring",
                    "Official SpaceX status updates",
                    "Technical readiness reports"
                ]
            }
            
            return json.dumps(confidence_assessment, indent=2)
            
        except Exception as e:
            return f"Error calculating confidence: {str(e)}"
    
    def get_agent(self) -> LlmAgent:
        """Get the configured LLM agent."""
        return self.agent


def create_coordinator_agent(model: str = "gemini-2.0-flash") -> LlmAgent:
    """Factory function to create a coordinator agent."""
    coordinator_agent = CoordinatorAgent(model)
    return coordinator_agent.get_agent()
