"""
Multi-Agent AI System for SpaceX Launch Analysis
Using Google ADK (Agent Development Kit)

This system demonstrates a sophisticated multi-agent architecture where:
1. PlannerAgent creates execution plans and routes tasks
2. SpaceXAgent fetches launch data and enriches with location info
3. WeatherAgent analyzes weather conditions for launch suitability
4. NewsAgent searches for relevant news and performs sentiment analysis
5. Coordinator<PERSON>gent synthesizes all data and provides final recommendations

Example goal: "Find the next SpaceX launch, check weather at that location, then summarize if it may be delayed."
"""

import asyncio
import json
from typing import Dict, Any, Optional
from google.adk.agents import SequentialAgent, LlmAgent
from google.adk.runtime import run_agent
from google.adk.sessions import Session

# Import our custom agents
from agents.planner_agent import create_planner_agent
from agents.spacex_agent import create_spacex_agent
from agents.weather_agent import create_weather_agent
from agents.news_agent import create_news_agent
from agents.coordinator_agent import create_coordinator_agent

# Import configuration
from config.settings import settings


class MultiAgentSystem:
    """
    Main multi-agent system that orchestrates the execution of specialized agents
    to achieve complex goals through data enrichment and iterative refinement.
    """
    
    def __init__(self, model: str = "gemini-2.0-flash"):
        self.model = model
        self.max_iterations = settings.MAX_ITERATIONS
        self.agents = self._create_agents()
        self.main_workflow = self._create_workflow()
    
    def _create_agents(self) -> Dict[str, LlmAgent]:
        """Create all specialized agents."""
        return {
            "planner": create_planner_agent(self.model),
            "spacex": create_spacex_agent(self.model),
            "weather": create_weather_agent(self.model),
            "news": create_news_agent(self.model),
            "coordinator": create_coordinator_agent(self.model)
        }
    
    def _create_workflow(self) -> SequentialAgent:
        """Create the main workflow that executes agents in sequence."""
        return SequentialAgent(
            name="SpaceXLaunchAnalysisWorkflow",
            description="Multi-agent system for analyzing SpaceX launches and delay probability",
            sub_agents=[
                self.agents["planner"],
                self.agents["spacex"],
                self.agents["weather"],
                self.agents["news"],
                self.agents["coordinator"]
            ]
        )
    
    async def execute_goal(self, user_goal: str) -> Dict[str, Any]:
        """
        Execute a user goal through the multi-agent system.
        
        Args:
            user_goal: The user's goal (e.g., "Find next SpaceX launch and check if weather may cause delays")
            
        Returns:
            Dictionary containing the final assessment and all intermediate data
        """
        print(f"🚀 Starting multi-agent analysis for goal: {user_goal}")
        print("=" * 80)
        
        # Create a new session for this execution
        session = Session()
        session.state["user_goal"] = user_goal
        session.state["iteration_count"] = 0
        
        try:
            # Execute the workflow
            print("📋 Phase 1: Planning and task routing...")
            result = await run_agent(self.main_workflow, user_goal, session=session)
            
            # Check if goal was achieved
            final_assessment = session.state.get("final_assessment", {})
            if isinstance(final_assessment, str):
                try:
                    final_assessment = json.loads(final_assessment)
                except:
                    final_assessment = {"error": "Could not parse final assessment"}
            
            goal_achievement = final_assessment.get("goal_achievement", {})
            goal_met = goal_achievement.get("goal_met", False)
            
            # If goal not met and we haven't exceeded max iterations, try again
            iteration_count = session.state.get("iteration_count", 0)
            if not goal_met and iteration_count < self.max_iterations:
                print(f"\n🔄 Goal not fully achieved. Starting iteration {iteration_count + 1}...")
                session.state["iteration_count"] = iteration_count + 1
                
                # Re-run with updated context
                result = await run_agent(self.main_workflow, 
                                       f"Refine analysis for: {user_goal}. Previous attempt was incomplete.", 
                                       session=session)
            
            # Compile final results
            final_results = {
                "user_goal": user_goal,
                "goal_achieved": goal_met,
                "iterations_used": session.state.get("iteration_count", 0) + 1,
                "execution_plan": session.state.get("execution_plan", {}),
                "spacex_data": session.state.get("spacex_data", {}),
                "weather_data": session.state.get("weather_data", {}),
                "news_data": session.state.get("news_data", {}),
                "final_assessment": final_assessment,
                "session_state": dict(session.state)
            }
            
            print("\n" + "=" * 80)
            print("✅ Multi-agent analysis complete!")
            self._print_summary(final_results)
            
            return final_results
            
        except Exception as e:
            print(f"❌ Error during execution: {str(e)}")
            return {
                "user_goal": user_goal,
                "error": str(e),
                "goal_achieved": False,
                "session_state": dict(session.state) if session else {}
            }
    
    def _print_summary(self, results: Dict[str, Any]):
        """Print a summary of the analysis results."""
        print(f"\n📊 ANALYSIS SUMMARY")
        print("-" * 40)
        
        goal_achieved = results.get("goal_achieved", False)
        print(f"Goal Achievement: {'✅ SUCCESS' if goal_achieved else '⚠️ PARTIAL'}")
        print(f"Iterations Used: {results.get('iterations_used', 1)}")
        
        # Extract key findings
        final_assessment = results.get("final_assessment", {})
        if isinstance(final_assessment, dict):
            delay_assessment = final_assessment.get("delay_assessment", {})
            if delay_assessment:
                probability = delay_assessment.get("probability", "Unknown")
                risk_level = delay_assessment.get("risk_level", "unknown")
                print(f"Delay Probability: {probability}")
                print(f"Risk Level: {risk_level.upper()}")
                
                primary_factors = delay_assessment.get("primary_factors", [])
                if primary_factors:
                    print(f"Primary Risk Factors:")
                    for factor in primary_factors[:3]:
                        print(f"  • {factor}")
            
            recommendations = final_assessment.get("recommendations", {})
            immediate_actions = recommendations.get("immediate_actions", [])
            if immediate_actions:
                print(f"Immediate Actions:")
                for action in immediate_actions[:2]:
                    print(f"  • {action}")


async def main():
    """Main function to run the multi-agent system."""
    # Validate configuration
    if not settings.validate():
        print("❌ Configuration validation failed. Please check your .env file.")
        return
    
    print("🤖 Multi-Agent SpaceX Launch Analysis System")
    print("Powered by Google ADK (Agent Development Kit)")
    print("=" * 80)
    
    # Initialize the system
    system = MultiAgentSystem()
    
    # Example goals to demonstrate the system
    example_goals = [
        "Find the next SpaceX launch, check weather at that location, then summarize if it may be delayed.",
        "Get information about upcoming SpaceX missions and analyze potential weather-related delays.",
        "Analyze the next Falcon 9 launch for any factors that might cause postponement."
    ]
    
    print("Example goals you can try:")
    for i, goal in enumerate(example_goals, 1):
        print(f"{i}. {goal}")
    
    print("\nEnter your goal (or press Enter for default example):")
    user_input = input("> ").strip()
    
    if not user_input:
        user_goal = example_goals[0]  # Use default example
        print(f"Using default goal: {user_goal}")
    else:
        user_goal = user_input
    
    # Execute the goal
    results = await system.execute_goal(user_goal)
    
    # Optionally save results to file
    save_results = input("\nSave detailed results to file? (y/n): ").strip().lower()
    if save_results == 'y':
        filename = f"analysis_results_{int(asyncio.get_event_loop().time())}.json"
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"Results saved to {filename}")


if __name__ == "__main__":
    asyncio.run(main())
