# Multi-Agent AI System Using Google ADK

A sophisticated multi-agent system built with Google's Agent Development Kit (ADK) that demonstrates advanced AI collaboration, data enrichment, and iterative refinement. The system takes complex user goals and achieves them through coordinated agent interactions.

## 🚀 Project Overview

This system showcases a real-world example of multi-agent AI coordination:

**Example Goal**: *"Find the next SpaceX launch, check weather at that location, then summarize if it may be delayed."*

**Agent Flow**:
1. **Planner Agent** → Analyzes goal and creates execution plan
2. **SpaceX Agent** → Fetches launch data and location information  
3. **Weather Agent** → Analyzes weather conditions for launch suitability
4. **News Agent** → Searches for relevant news and performs sentiment analysis
5. **Coordinator Agent** → Synthesizes all data and provides final assessment

## ✨ Key Features

- **🤖 Multi-Agent Coordination**: 5 specialized agents working in sequence
- **📊 Data Enrichment**: Each agent enriches the output of the previous
- **🔄 Iterative Refinement**: System iterates if goals aren't fully met
- **🌐 Real API Integration**: Uses 4 different APIs for real-world data
- **📈 Comprehensive Evaluation**: Built-in goal satisfaction and trajectory testing
- **🛡️ Robust Error Handling**: Graceful degradation and fallback strategies

## 🏗️ Architecture

```
User Goal → Planner → SpaceX → Weather → News → Coordinator → Final Assessment
              ↓         ↓        ↓       ↓         ↓
         Execution   Launch   Weather  News    Synthesized
           Plan      Data    Analysis Analysis  Recommendations
```

### Agent Responsibilities

| Agent | Purpose | Input | Output |
|-------|---------|-------|--------|
| **Planner** | Goal analysis & routing | User goal | Execution plan |
| **SpaceX** | Launch data acquisition | Goal context | Mission data + location |
| **Weather** | Weather analysis | Launch location | Weather risk assessment |
| **News** | Media sentiment analysis | Mission name | News sentiment + delay indicators |
| **Coordinator** | Data synthesis | All agent outputs | Final delay assessment |

## 🛠️ Setup Instructions

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd enshrine_proj
pip install -r requirements.txt
```

### 2. Configure API Keys

Copy the environment template:
```bash
cp .env.example .env
```

Edit `.env` with your API keys:
```bash
# Required APIs
GOOGLE_API_KEY=your_google_api_key
OPENWEATHER_API_KEY=your_openweather_key  
NEWS_API_KEY=your_news_api_key

# Optional
GOOGLE_CLOUD_PROJECT=your_project_id
```

### 3. API Setup Guide

See [docs/api_setup.md](docs/api_setup.md) for detailed API configuration instructions.

**Required APIs:**
- **Google Cloud/Gemini** (LLM models) - Free tier available
- **OpenWeatherMap** (Weather data) - 1,000 free calls/day
- **NewsAPI** (News articles) - 1,000 free requests/day  
- **SpaceX API** (Launch data) - Completely free, no key required

### 4. Verify Setup

```bash
python -c "from config.settings import settings; print('✅ Valid' if settings.validate() else '❌ Invalid')"
```

## 🚀 Usage

### Basic Usage

```bash
python main.py
```

The system will prompt you for a goal or use the default example:
```
"Find the next SpaceX launch, check weather at that location, then summarize if it may be delayed."
```

### Example Goals

Try these example goals:
- *"Get information about upcoming SpaceX missions and analyze potential weather-related delays."*
- *"Analyze the next Falcon 9 launch for any factors that might cause postponement."*
- *"Find SpaceX launch schedule and assess launch conditions."*

### Programmatic Usage

```python
from main import MultiAgentSystem

system = MultiAgentSystem()
results = await system.execute_goal("Your goal here")

print(f"Goal Achieved: {results['goal_achieved']}")
print(f"Delay Assessment: {results['final_assessment']['delay_assessment']}")
```

## 📊 Evaluation System

The project includes comprehensive evaluation tools:

### Goal Satisfaction Testing
```bash
python evaluations/test_goal_satisfaction.py
```
Tests whether the system successfully achieves different types of user goals.

### Agent Trajectory Testing  
```bash
python evaluations/test_agent_trajectory.py
```
Evaluates agent chaining, data enrichment flow, and iterative refinement.

### Evaluation Metrics
- **Goal Achievement Rate**: Percentage of goals successfully completed
- **Data Enrichment Quality**: How well each agent adds value
- **Agent Coordination**: Effectiveness of agent interactions
- **Iteration Efficiency**: Success rate of iterative refinement

## 📁 Project Structure

```
enshrine_proj/
├── agents/                    # Specialized AI agents
│   ├── planner_agent.py      # Goal analysis and routing
│   ├── spacex_agent.py       # SpaceX data acquisition
│   ├── weather_agent.py      # Weather analysis
│   ├── news_agent.py         # News sentiment analysis
│   └── coordinator_agent.py  # Data synthesis
├── tools/                     # API integration tools
│   ├── spacex_api.py         # SpaceX API client
│   ├── weather_api.py        # OpenWeatherMap client
│   └── news_api.py           # NewsAPI client
├── evaluations/              # Evaluation and testing
│   ├── test_goal_satisfaction.py
│   └── test_agent_trajectory.py
├── config/                   # Configuration management
│   └── settings.py
├── docs/                     # Documentation
│   ├── architecture.md       # System architecture
│   ├── agent_flow.md         # Agent interaction flow
│   └── api_setup.md          # API setup guide
├── main.py                   # Main application
├── requirements.txt          # Dependencies
└── .env.example             # Environment template
```

## 🔧 Technical Details

### Built With
- **Google ADK**: Multi-agent framework and LLM integration
- **Python 3.8+**: Core programming language
- **AsyncIO**: Asynchronous API calls and agent coordination
- **Pydantic**: Data validation and settings management
- **HTTPX**: Modern HTTP client for API calls

### Key Design Patterns
- **Sequential Agent Pipeline**: Ensures proper data flow dependencies
- **Shared Session State**: Enables data passing between agents
- **Function Tools**: Modular capabilities for each agent
- **Iterative Refinement**: Automatic goal achievement optimization
- **Graceful Degradation**: System continues with partial data

### Performance Features
- **Configurable Timeouts**: Prevents hanging on slow APIs
- **Rate Limit Handling**: Respects API limits with backoff
- **Error Isolation**: Agent failures don't crash the system
- **Caching Strategy**: Reduces redundant API calls

## 📈 Example Output

```
🚀 Starting multi-agent analysis for goal: Find the next SpaceX launch, check weather at that location, then summarize if it may be delayed.

📋 Phase 1: Planning and task routing...
✅ Execution plan created with 5 agent steps

🛰️ Phase 2: SpaceX data acquisition...
✅ Found mission: Starlink 6-34, Launch: 2024-01-15 10:30 UTC
✅ Location: Kennedy Space Center LC-39A (28.6080, -80.6040)

🌤️ Phase 3: Weather analysis...
✅ Current conditions: 22°C, Wind 8.5 m/s, Partly cloudy
✅ Launch suitability: FAVORABLE (Low risk)

📰 Phase 4: News analysis...
✅ Found 8 relevant articles, Sentiment: POSITIVE
✅ No delay indicators detected

🎯 Phase 5: Final assessment...
✅ Delay Probability: Low (15%)
✅ Risk Level: LOW
✅ Recommendation: Continue nominal preparations

📊 ANALYSIS SUMMARY
Goal Achievement: ✅ SUCCESS
Delay Probability: Low (15%)
Risk Level: LOW
Primary Risk Factors: None identified
Immediate Actions: • Continue nominal preparations
```

## 🧪 Testing and Validation

### Run All Tests
```bash
# Goal satisfaction evaluation
python evaluations/test_goal_satisfaction.py

# Agent trajectory evaluation  
python evaluations/test_agent_trajectory.py
```

### Test Individual Components
```bash
# Test API integrations
python -m tools.spacex_api
python -m tools.weather_api
python -m tools.news_api

# Test individual agents
python -c "from agents.spacex_agent import create_spacex_agent; print('SpaceX Agent OK')"
```

## 🤝 Contributing

This project demonstrates advanced multi-agent AI concepts. Areas for contribution:

1. **Additional Agents**: Create new specialized agents (e.g., financial analysis, social media sentiment)
2. **New APIs**: Integrate additional data sources
3. **Evaluation Metrics**: Develop new evaluation criteria
4. **Performance Optimization**: Improve agent coordination efficiency
5. **UI/UX**: Build a web interface for the system

## 📚 Documentation

- [Architecture Overview](docs/architecture.md) - System design and components
- [Agent Flow](docs/agent_flow.md) - Detailed agent interaction patterns  
- [API Setup](docs/api_setup.md) - Complete API configuration guide

## 🎯 Assignment Requirements Met

✅ **Multi-Agent System**: 5 specialized agents with clear responsibilities  
✅ **Google ADK Integration**: Built entirely on Google's Agent Development Kit  
✅ **Agent Chaining**: Sequential data enrichment with dependencies  
✅ **Data Enrichment**: Each agent adds value to previous agent's output  
✅ **Iterative Refinement**: System iterates if goals aren't achieved  
✅ **Multiple APIs**: SpaceX, OpenWeatherMap, NewsAPI, Google Cloud  
✅ **Planner Logic**: Sophisticated goal analysis and routing  
✅ **Comprehensive Evaluation**: Goal satisfaction and trajectory testing  
✅ **Complete Documentation**: Architecture, flow, and setup guides  
✅ **Production Ready**: Error handling, configuration management, testing

## 📄 License

This project is created for educational and demonstration purposes. Please respect API terms of service and usage limits.

---

**Built with ❤️ using Google ADK - Demonstrating the future of multi-agent AI systems**
