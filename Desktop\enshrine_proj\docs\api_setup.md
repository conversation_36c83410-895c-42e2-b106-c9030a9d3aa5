# API Setup Guide

## Overview

This guide walks you through setting up all the required APIs for the multi-agent system. The system uses several free and freemium APIs to gather comprehensive data about SpaceX launches, weather conditions, and news coverage.

## Required APIs

### 1. Google Cloud / Gemini API (Required)
**Purpose**: Powers the LLM agents using Google's Gemini models

**Setup Steps:**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing project
3. Enable the Vertex AI API
4. Go to "APIs & Services" → "Credentials"
5. Click "Create Credentials" → "API Key"
6. Copy the API key

**Configuration:**
```bash
GOOGLE_API_KEY=your_api_key_here
GOOGLE_CLOUD_PROJECT=your_project_id_here
```

**Pricing**: 
- Free tier available with usage limits
- Pay-per-use after free tier
- Estimated cost: $0.01-0.10 per analysis

### 2. OpenWeatherMap API (Required)
**Purpose**: Provides weather data and forecasts for launch locations

**Setup Steps:**
1. Go to [OpenWeatherMap](https://openweathermap.org/api)
2. Sign up for a free account
3. Navigate to "API Keys" in your account dashboard
4. Copy the default API key (or create a new one)

**Configuration:**
```bash
OPENWEATHER_API_KEY=your_api_key_here
```

**Free Tier Limits:**
- 1,000 API calls per day
- 60 calls per minute
- Current weather and 5-day forecast included

**Pricing**: 
- Free tier sufficient for development and testing
- Paid plans start at $40/month for higher limits

### 3. NewsAPI (Required)
**Purpose**: Searches for news articles related to SpaceX missions

**Setup Steps:**
1. Go to [NewsAPI](https://newsapi.org/)
2. Sign up for a free account
3. Go to your account dashboard
4. Copy your API key

**Configuration:**
```bash
NEWS_API_KEY=your_api_key_here
```

**Free Tier Limits:**
- 1,000 requests per day
- Developer plan (free) includes everything endpoint
- No commercial use on free tier

**Pricing**: 
- Free tier perfect for development
- Business plans start at $449/month

### 4. SpaceX API (No Key Required)
**Purpose**: Provides SpaceX launch data and mission information

**Setup**: No setup required! SpaceX provides a free, open API.

**Endpoints Used:**
- `https://api.spacexdata.com/v4/launches/next` - Next launch
- `https://api.spacexdata.com/v4/launches/upcoming` - Upcoming launches
- `https://api.spacexdata.com/v4/launchpads/{id}` - Launch pad details

**Rate Limits**: No official limits, but be respectful

## Environment Configuration

### 1. Create .env File
Copy the example environment file:
```bash
cp .env.example .env
```

### 2. Fill in Your API Keys
Edit the `.env` file with your actual API keys:
```bash
# Google Cloud / Gemini API Configuration
GOOGLE_API_KEY=your_actual_google_api_key
GOOGLE_CLOUD_PROJECT=your_actual_project_id

# OpenWeatherMap API
OPENWEATHER_API_KEY=your_actual_openweather_key

# NewsAPI
NEWS_API_KEY=your_actual_news_api_key

# Optional: Additional APIs
COINGECKO_API_KEY=optional_coingecko_key

# Application Settings
LOG_LEVEL=INFO
MAX_ITERATIONS=3
TIMEOUT_SECONDS=30
```

### 3. Verify Configuration
Run the configuration validator:
```bash
python -c "from config.settings import settings; print('✅ Valid' if settings.validate() else '❌ Invalid')"
```

## API Testing

### Test Individual APIs

**Test SpaceX API:**
```bash
curl https://api.spacexdata.com/v4/launches/next
```

**Test OpenWeatherMap API:**
```bash
curl "https://api.openweathermap.org/data/2.5/weather?q=London&appid=YOUR_API_KEY"
```

**Test NewsAPI:**
```bash
curl "https://newsapi.org/v2/everything?q=SpaceX&apiKey=YOUR_API_KEY"
```

### Test System Integration
Run the built-in API tests:
```bash
python -m tools.spacex_api
python -m tools.weather_api
python -m tools.news_api
```

## API Usage Optimization

### Rate Limiting Best Practices

**OpenWeatherMap:**
- Cache weather data for 30 minutes
- Batch location requests when possible
- Use appropriate forecast intervals

**NewsAPI:**
- Cache news searches for 1 hour
- Use specific date ranges to reduce results
- Combine related search terms

**SpaceX API:**
- Cache launch data for 1 hour
- Minimize redundant launch pad requests
- Use upcoming launches endpoint efficiently

### Error Handling

**Network Timeouts:**
```python
# All APIs configured with 30-second timeout
TIMEOUT_SECONDS=30
```

**API Key Issues:**
- Verify keys are correctly copied (no extra spaces)
- Check API key permissions and quotas
- Ensure billing is set up for paid APIs

**Rate Limit Handling:**
```python
# Built-in retry logic with exponential backoff
# Graceful degradation when APIs are unavailable
```

## Security Best Practices

### API Key Security
1. **Never commit API keys to version control**
2. **Use environment variables only**
3. **Rotate keys regularly**
4. **Use minimal permissions**

### Production Considerations
1. **Use separate API keys for development/production**
2. **Monitor API usage and costs**
3. **Set up billing alerts**
4. **Implement proper logging (without exposing keys)**

## Troubleshooting

### Common Issues

**"Invalid API Key" Errors:**
- Double-check key is correctly copied
- Verify key hasn't expired
- Check if billing is required and set up

**"Rate Limit Exceeded":**
- Wait for rate limit reset
- Implement caching to reduce requests
- Consider upgrading to paid tier

**"Network Timeout":**
- Check internet connection
- Verify API endpoints are accessible
- Increase timeout if needed

**"No Data Returned":**
- Verify API endpoints are correct
- Check if service is experiencing outages
- Validate request parameters

### Debug Mode
Enable debug logging to troubleshoot API issues:
```bash
LOG_LEVEL=DEBUG
```

This will show detailed API request/response information.

### API Status Monitoring
Check API status pages:
- [Google Cloud Status](https://status.cloud.google.com/)
- [OpenWeatherMap Status](https://openweathermap.statuspage.io/)
- [NewsAPI Status](https://newsapi.statuspage.io/)

## Cost Management

### Free Tier Monitoring
Track your usage to stay within free tiers:

**Google Cloud:**
- Monitor usage in Cloud Console
- Set up billing alerts

**OpenWeatherMap:**
- Check usage in account dashboard
- Monitor daily call count

**NewsAPI:**
- Track requests in developer dashboard
- Monitor monthly quota

### Cost Optimization Tips
1. **Cache aggressively** to reduce API calls
2. **Use specific queries** to get relevant data faster
3. **Implement circuit breakers** for failing APIs
4. **Monitor and alert** on unusual usage patterns

## Alternative APIs (Optional)

If you encounter issues with the primary APIs, consider these alternatives:

**Weather Alternatives:**
- WeatherAPI.com (free tier available)
- AccuWeather API (limited free tier)
- Weather Underground API

**News Alternatives:**
- Bing News Search API (part of Azure Cognitive Services)
- Guardian API (free with registration)
- Reddit API (for community sentiment)

**Additional Data Sources:**
- CoinGecko API (cryptocurrency data, free)
- Alpha Vantage (financial data, free tier)
- NASA APIs (space-related data, free)

## Support and Resources

### Documentation Links
- [Google Cloud AI Documentation](https://cloud.google.com/ai-platform/docs)
- [OpenWeatherMap API Docs](https://openweathermap.org/api)
- [NewsAPI Documentation](https://newsapi.org/docs)
- [SpaceX API Documentation](https://github.com/r-spacex/SpaceX-API)

### Community Support
- [Google Cloud Community](https://cloud.google.com/community)
- [OpenWeatherMap Community](https://openweathermap.org/community)
- [SpaceX API Reddit](https://www.reddit.com/r/SpaceXAPI/)

### Getting Help
If you encounter issues:
1. Check the troubleshooting section above
2. Review API documentation
3. Check API status pages
4. Contact API support if needed

Remember to keep your API keys secure and monitor your usage to avoid unexpected charges!
