# Agent Flow Documentation

## Overview

This document details the step-by-step flow of agent interactions, data enrichment patterns, and decision-making processes in the multi-agent system.

## Complete Agent Flow

### Phase 1: Planning and Goal Analysis

```mermaid
graph TD
    A[User Goal Input] --> B[Planner Agent]
    B --> C{Goal Analysis}
    C --> D[Create Execution Plan]
    D --> E[Validate Plan Logic]
    E --> F[Store Plan in Session State]
    F --> G[Route to SpaceX Agent]
```

**Planner Agent Process:**
1. **Goal Decomposition**: Break down user request into actionable steps
2. **Agent Selection**: Determine which agents are needed
3. **Dependency Mapping**: Identify data dependencies between agents
4. **Success Criteria**: Define what constitutes goal achievement
5. **Plan Storage**: Save structured plan to `state['execution_plan']`

**Example Plan Output:**
```json
{
  "goal": "Find next SpaceX launch and check if weather may cause delays",
  "steps": [
    {"agent": "SpaceXAgent", "action": "fetch_next_launch", "output_key": "spacex_data"},
    {"agent": "WeatherAgent", "action": "analyze_launch_weather", "input_from": "spacex_data"},
    {"agent": "NewsAgent", "action": "search_mission_news", "input_from": "spacex_data"},
    {"agent": "CoordinatorAgent", "action": "synthesize_assessment"}
  ],
  "success_criteria": "Provide delay probability with supporting evidence"
}
```

### Phase 2: Data Acquisition

```mermaid
graph TD
    A[SpaceX Agent Activated] --> B[Fetch Next Launch API]
    B --> C[Process Launch Data]
    C --> D[Extract Location Coordinates]
    D --> E[Get Launch Pad Info]
    E --> F[Calculate Timeline]
    F --> G[Enrich with Context]
    G --> H[Store to spacex_data]
    H --> I[Trigger Weather Agent]
```

**SpaceX Agent Process:**
1. **API Call**: `GET https://api.spacexdata.com/v4/launches/next`
2. **Data Processing**: Extract mission name, date, launch pad ID
3. **Location Enrichment**: Fetch launch pad coordinates
4. **Timeline Calculation**: Compute time until launch
5. **Context Addition**: Add mission criticality and details

**Data Enrichment Example:**
```json
{
  "mission_name": "Starlink 6-34",
  "launch_date": "2024-01-15T10:30:00Z",
  "launch_location": {
    "name": "Kennedy Space Center LC-39A",
    "coordinates": {"lat": 28.6080, "lon": -80.6040},
    "region": "Florida, USA"
  },
  "timeline": {"days_until_launch": 3, "urgency": "medium"},
  "mission_details": "Starlink satellite deployment mission"
}
```

### Phase 3: Weather Analysis

```mermaid
graph TD
    A[Weather Agent Activated] --> B[Extract Coordinates from SpaceX Data]
    B --> C[Fetch Current Weather]
    C --> D[Get Weather Forecast]
    D --> E[Analyze Launch Conditions]
    E --> F[Assess Risk Factors]
    F --> G[Generate Recommendations]
    G --> H[Store to weather_data]
    H --> I[Trigger News Agent]
```

**Weather Agent Process:**
1. **Coordinate Extraction**: Read location from `state['spacex_data']`
2. **Current Weather**: API call to OpenWeatherMap
3. **Forecast Data**: Get 3-5 day forecast for launch window
4. **Launch Analysis**: Apply launch weather criteria
5. **Risk Assessment**: Identify potential weather delays

**Weather Analysis Criteria:**
- **Wind Speed**: < 15 m/s for safe launch
- **Precipitation**: No rain/snow during launch window
- **Cloud Coverage**: < 80% for visibility
- **Temperature**: Within operational limits
- **Lightning**: No storms within 10 nautical miles

**Weather Data Output:**
```json
{
  "location": {"name": "Kennedy Space Center", "coordinates": {...}},
  "current_weather": {
    "temperature": 22,
    "wind_speed": 8.5,
    "description": "partly cloudy",
    "humidity": 65
  },
  "launch_analysis": {
    "suitable": true,
    "risk_level": "low",
    "issues": [],
    "recommendations": "Conditions favorable for launch"
  }
}
```

### Phase 4: News and Sentiment Analysis

```mermaid
graph TD
    A[News Agent Activated] --> B[Extract Mission Name from SpaceX Data]
    B --> C[Search Mission-Specific News]
    C --> D[Fetch General SpaceX News]
    D --> E[Analyze Article Sentiment]
    E --> F[Identify Delay Indicators]
    F --> G[Assess Media Risk]
    G --> H[Store to news_data]
    H --> I[Trigger Coordinator Agent]
```

**News Agent Process:**
1. **Mission Context**: Extract mission name from SpaceX data
2. **Targeted Search**: Search for mission-specific articles
3. **Sentiment Analysis**: Analyze positive/negative indicators
4. **Delay Detection**: Look for delay-related keywords
5. **Risk Assessment**: Evaluate media-based risk factors

**Sentiment Analysis Keywords:**
- **Positive**: "ready", "on schedule", "successful", "go for launch"
- **Negative**: "delay", "postpone", "technical issue", "weather concern"
- **Neutral**: General mission information without sentiment

**News Data Output:**
```json
{
  "mission_name": "Starlink 6-34",
  "search_results": {"total_articles": 15, "relevant_articles": 8},
  "sentiment_analysis": {
    "overall_sentiment": "positive",
    "confidence": "medium",
    "positive_indicators": 5,
    "negative_indicators": 1
  },
  "delay_indicators": [],
  "media_risk_assessment": {"risk_level": "low"}
}
```

### Phase 5: Data Synthesis and Final Assessment

```mermaid
graph TD
    A[Coordinator Agent Activated] --> B[Collect All Agent Data]
    B --> C[Synthesize Information]
    C --> D[Calculate Delay Probability]
    D --> E[Identify Risk Factors]
    E --> F[Generate Recommendations]
    F --> G[Assess Goal Achievement]
    G --> H[Store Final Assessment]
    H --> I{Goal Achieved?}
    I -->|Yes| J[Complete]
    I -->|No| K[Iterate if < Max Iterations]
    K --> L[Refine Analysis]
    L --> A
```

**Coordinator Agent Process:**
1. **Data Collection**: Gather data from all previous agents
2. **Risk Scoring**: Calculate weighted risk score
3. **Probability Assessment**: Determine delay likelihood
4. **Recommendation Generation**: Provide actionable advice
5. **Goal Evaluation**: Check if original goal was achieved

**Risk Scoring Algorithm:**
```python
risk_score = 0
if weather_risk == "high": risk_score += 3
elif weather_risk == "medium": risk_score += 1

if news_sentiment == "negative": risk_score += 2
elif news_sentiment == "neutral": risk_score += 1

if timeline_urgency == "high": risk_score += 1

delay_probability = min(risk_score * 15, 85)  # Cap at 85%
```

**Final Assessment Output:**
```json
{
  "delay_assessment": {
    "probability": "Low (15%)",
    "risk_level": "low",
    "primary_factors": ["Favorable weather conditions", "Positive media coverage"],
    "timeline_impact": "No expected delays"
  },
  "recommendations": {
    "immediate_actions": ["Continue nominal preparations"],
    "monitoring_points": ["Weather updates", "Technical readiness"],
    "decision_timeline": "Final go/no-go 4 hours before launch"
  },
  "confidence": {"level": "high", "reasoning": "Complete data from all sources"}
}
```

## Data Enrichment Patterns

### 1. Sequential Enrichment
Each agent adds value to the data chain:
- **SpaceX**: Raw launch data → Enriched mission context
- **Weather**: Location coordinates → Weather risk assessment
- **News**: Mission name → Media sentiment analysis
- **Coordinator**: All data → Synthesized recommendations

### 2. Contextual Enhancement
Agents don't just add data; they add context:
- **SpaceX**: Adds timeline urgency and mission criticality
- **Weather**: Adds launch-specific weather criteria
- **News**: Adds historical context and expert opinions
- **Coordinator**: Adds decision-making framework

### 3. Cross-Agent Validation
Later agents can validate earlier agent findings:
- **News** can confirm or contradict **Weather** concerns
- **Coordinator** can identify inconsistencies between agents
- **Iteration** allows refinement based on validation results

## Iterative Refinement Process

### Iteration Triggers
1. **Goal Not Achieved**: Completeness score < 80%
2. **Data Quality Issues**: Missing critical information
3. **Conflicting Information**: Agents provide contradictory data
4. **Low Confidence**: Uncertainty in final assessment

### Refinement Strategies
1. **Data Gap Filling**: Re-run agents with more specific queries
2. **Analysis Deepening**: Request more detailed analysis
3. **Context Addition**: Provide additional context from previous iteration
4. **Validation Focus**: Cross-check conflicting information

### Iteration Example
```
Iteration 1: Basic analysis with limited news coverage
→ Low confidence due to insufficient news data
→ Iteration 2: Expanded news search with broader keywords
→ Higher confidence with more comprehensive analysis
```

## Error Handling Flow

### API Failures
```mermaid
graph TD
    A[API Call] --> B{Success?}
    B -->|Yes| C[Process Data]
    B -->|No| D[Log Error]
    D --> E[Try Fallback]
    E --> F{Fallback Success?}
    F -->|Yes| G[Continue with Limited Data]
    F -->|No| H[Report Failure]
    H --> I[Continue to Next Agent]
```

### Agent Failures
- **Isolation**: Failed agent doesn't stop workflow
- **Graceful Degradation**: System continues with available data
- **Error Reporting**: Clear indication of what data is missing
- **Partial Success**: Provide results based on successful agents

## Performance Optimization

### Parallel Execution Opportunities
- **Weather + News**: Can run simultaneously after SpaceX
- **API Calls**: Multiple weather endpoints can be called in parallel
- **Data Processing**: Enrichment functions can be parallelized

### Caching Strategies
- **Launch Data**: Cache for short periods (launches don't change frequently)
- **Weather Data**: Cache for 30 minutes (weather updates regularly)
- **News Data**: Cache for 1 hour (news doesn't change rapidly)

### Resource Management
- **Timeout Handling**: All API calls have configurable timeouts
- **Rate Limiting**: Respect API rate limits with backoff strategies
- **Memory Management**: Clear session state after completion

This flow ensures reliable, efficient, and comprehensive analysis while maintaining flexibility for different types of goals and handling various failure scenarios.
