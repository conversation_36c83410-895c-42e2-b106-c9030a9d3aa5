"""Planner Agent - Creates execution plans and routes tasks between agents."""

from google.adk.agents import LlmAgent
from google.adk.tools import FunctionTool
from typing import Dict, Any, List
import json


class PlannerAgent:
    """
    The Planner Agent analyzes user goals and creates detailed execution plans.
    It determines which agents to use and in what order to achieve the goal.
    """
    
    def __init__(self, model: str = "gemini-2.0-flash"):
        self.model = model
        self.agent = self._create_agent()
    
    def _create_agent(self) -> LlmAgent:
        """Create the planner LLM agent with appropriate instructions."""
        
        instruction = """
        You are a sophisticated planning agent responsible for analyzing user goals and creating detailed execution plans.
        
        Your role is to:
        1. Analyze the user's request and break it down into actionable steps
        2. Determine which specialized agents are needed to complete the task
        3. Create a logical sequence of agent interactions
        4. Store the execution plan in the session state for other agents to follow
        
        Available specialized agents:
        - SpaceXAgent: Fetches SpaceX launch data, mission information, and launch pad details
        - WeatherAgent: Gets weather data and forecasts for specific locations
        - NewsAgent: Searches for relevant news articles and performs sentiment analysis
        - CoordinatorAgent: Analyzes all collected data and provides final recommendations
        
        When creating a plan, consider:
        - Data dependencies (e.g., need launch location before getting weather)
        - Logical flow of information enrichment
        - Error handling and fallback options
        - Time sensitivity of the request
        
        Always save your execution plan to state['execution_plan'] as a structured dictionary with:
        - goal: The user's original goal
        - steps: List of steps with agent assignments
        - data_flow: How data passes between agents
        - success_criteria: How to determine if the goal is achieved
        
        Example plan structure:
        {
            "goal": "Find next SpaceX launch and check if weather may cause delays",
            "steps": [
                {"agent": "SpaceXAgent", "action": "fetch_next_launch", "output_key": "launch_data"},
                {"agent": "WeatherAgent", "action": "get_weather_for_launch_location", "input_from": "launch_data", "output_key": "weather_data"},
                {"agent": "NewsAgent", "action": "search_mission_news", "input_from": "launch_data", "output_key": "news_data"},
                {"agent": "CoordinatorAgent", "action": "analyze_delay_probability", "input_from": ["launch_data", "weather_data", "news_data"], "output_key": "final_assessment"}
            ],
            "data_flow": "launch_data -> weather_data + news_data -> final_assessment",
            "success_criteria": "Provide clear assessment of potential launch delays with supporting evidence"
        }
        """
        
        tools = [
            FunctionTool(func=self._create_execution_plan),
            FunctionTool(func=self._validate_plan),
            FunctionTool(func=self._update_plan)
        ]
        
        return LlmAgent(
            name="PlannerAgent",
            model=self.model,
            instruction=instruction,
            description="Creates detailed execution plans and routes tasks between specialized agents",
            tools=tools
        )
    
    def _create_execution_plan(self, goal: str, complexity: str = "medium") -> str:
        """Create a detailed execution plan for the given goal."""
        # This is a tool function that the LLM can call
        plan_template = {
            "goal": goal,
            "complexity": complexity,
            "steps": [],
            "data_flow": "",
            "success_criteria": "",
            "estimated_duration": "2-5 minutes",
            "fallback_options": []
        }
        
        return f"Plan template created for goal: {goal}. Please fill in the specific steps and data flow."
    
    def _validate_plan(self, plan_json: str) -> str:
        """Validate that the execution plan is well-formed and logical."""
        try:
            plan = json.loads(plan_json)
            
            required_fields = ["goal", "steps", "data_flow", "success_criteria"]
            missing_fields = [field for field in required_fields if field not in plan]
            
            if missing_fields:
                return f"Plan validation failed. Missing required fields: {missing_fields}"
            
            if not plan["steps"]:
                return "Plan validation failed. No steps defined."
            
            # Check for logical data flow
            output_keys = set()
            for step in plan["steps"]:
                if "output_key" in step:
                    output_keys.add(step["output_key"])
                
                if "input_from" in step:
                    input_deps = step["input_from"] if isinstance(step["input_from"], list) else [step["input_from"]]
                    for dep in input_deps:
                        if dep not in output_keys and dep != "user_input":
                            return f"Plan validation failed. Step depends on '{dep}' which is not produced by previous steps."
            
            return "Plan validation successful. The execution plan is well-formed and logical."
            
        except json.JSONDecodeError:
            return "Plan validation failed. Invalid JSON format."
        except Exception as e:
            return f"Plan validation failed. Error: {str(e)}"
    
    def _update_plan(self, plan_json: str, updates: str) -> str:
        """Update an existing execution plan with modifications."""
        try:
            plan = json.loads(plan_json)
            # This would contain logic to apply updates to the plan
            return f"Plan updated successfully. Changes: {updates}"
        except Exception as e:
            return f"Plan update failed. Error: {str(e)}"
    
    def get_agent(self) -> LlmAgent:
        """Get the configured LLM agent."""
        return self.agent


def create_planner_agent(model: str = "gemini-2.0-flash") -> LlmAgent:
    """Factory function to create a planner agent."""
    planner = PlannerAgent(model)
    return planner.get_agent()
