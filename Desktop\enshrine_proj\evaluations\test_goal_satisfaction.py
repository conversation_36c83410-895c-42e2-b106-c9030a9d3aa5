"""
Goal Satisfaction Evaluation

This module tests whether the multi-agent system successfully achieves user goals
and provides meaningful, accurate results.
"""

import asyncio
import json
import pytest
from typing import Dict, Any, List
from main import MultiAgentSystem
from config.settings import settings


class GoalSatisfactionEvaluator:
    """Evaluates how well the system satisfies different types of user goals."""
    
    def __init__(self):
        self.system = MultiAgentSystem()
        self.test_goals = [
            {
                "goal": "Find the next SpaceX launch, check weather at that location, then summarize if it may be delayed.",
                "expected_components": ["spacex_data", "weather_data", "news_data", "final_assessment"],
                "success_criteria": {
                    "has_launch_info": True,
                    "has_weather_analysis": True,
                    "has_delay_assessment": True,
                    "provides_recommendations": True
                }
            },
            {
                "goal": "Get upcoming SpaceX missions and analyze weather conditions for potential delays.",
                "expected_components": ["spacex_data", "weather_data", "final_assessment"],
                "success_criteria": {
                    "has_launch_info": True,
                    "has_weather_analysis": True,
                    "has_delay_assessment": True
                }
            },
            {
                "goal": "Analyze news coverage of the next Falcon 9 launch for delay indicators.",
                "expected_components": ["spacex_data", "news_data", "final_assessment"],
                "success_criteria": {
                    "has_launch_info": True,
                    "has_news_analysis": True,
                    "has_sentiment_analysis": True
                }
            }
        ]
    
    async def evaluate_goal_satisfaction(self, goal_config: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate satisfaction for a specific goal."""
        goal = goal_config["goal"]
        expected_components = goal_config["expected_components"]
        success_criteria = goal_config["success_criteria"]
        
        print(f"\n🎯 Evaluating goal: {goal}")
        print("-" * 60)
        
        try:
            # Execute the goal
            results = await self.system.execute_goal(goal)
            
            # Check component presence
            component_scores = {}
            for component in expected_components:
                has_component = component in results and results[component]
                component_scores[component] = has_component
                print(f"✅ {component}: {'Present' if has_component else 'Missing'}")
            
            # Evaluate success criteria
            criteria_scores = {}
            for criterion, expected in success_criteria.items():
                score = self._evaluate_criterion(criterion, results, expected)
                criteria_scores[criterion] = score
                print(f"📊 {criterion}: {'✅ Met' if score else '❌ Not Met'}")
            
            # Calculate overall satisfaction score
            component_satisfaction = sum(component_scores.values()) / len(component_scores)
            criteria_satisfaction = sum(criteria_scores.values()) / len(criteria_scores)
            overall_satisfaction = (component_satisfaction + criteria_satisfaction) / 2
            
            evaluation = {
                "goal": goal,
                "overall_satisfaction": overall_satisfaction,
                "component_satisfaction": component_satisfaction,
                "criteria_satisfaction": criteria_satisfaction,
                "component_scores": component_scores,
                "criteria_scores": criteria_scores,
                "goal_achieved": results.get("goal_achieved", False),
                "iterations_used": results.get("iterations_used", 0),
                "execution_time": "measured_in_real_implementation",
                "data_quality": self._assess_data_quality(results),
                "recommendations_quality": self._assess_recommendations_quality(results)
            }
            
            print(f"🏆 Overall Satisfaction: {overall_satisfaction:.2%}")
            return evaluation
            
        except Exception as e:
            print(f"❌ Error evaluating goal: {str(e)}")
            return {
                "goal": goal,
                "error": str(e),
                "overall_satisfaction": 0.0,
                "goal_achieved": False
            }
    
    def _evaluate_criterion(self, criterion: str, results: Dict[str, Any], expected: bool) -> bool:
        """Evaluate a specific success criterion."""
        if criterion == "has_launch_info":
            spacex_data = results.get("spacex_data", {})
            return bool(spacex_data and spacex_data.get("mission_name"))
        
        elif criterion == "has_weather_analysis":
            weather_data = results.get("weather_data", {})
            return bool(weather_data and weather_data.get("launch_analysis"))
        
        elif criterion == "has_delay_assessment":
            final_assessment = results.get("final_assessment", {})
            if isinstance(final_assessment, str):
                try:
                    final_assessment = json.loads(final_assessment)
                except:
                    return False
            return bool(final_assessment.get("delay_assessment"))
        
        elif criterion == "provides_recommendations":
            final_assessment = results.get("final_assessment", {})
            if isinstance(final_assessment, str):
                try:
                    final_assessment = json.loads(final_assessment)
                except:
                    return False
            return bool(final_assessment.get("recommendations"))
        
        elif criterion == "has_news_analysis":
            news_data = results.get("news_data", {})
            return bool(news_data and news_data.get("analysis"))
        
        elif criterion == "has_sentiment_analysis":
            news_data = results.get("news_data", {})
            if isinstance(news_data, str):
                try:
                    news_data = json.loads(news_data)
                except:
                    return False
            return bool(news_data.get("sentiment_analysis"))
        
        return False
    
    def _assess_data_quality(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Assess the quality of data gathered by agents."""
        quality_assessment = {
            "spacex_data_quality": "unknown",
            "weather_data_quality": "unknown",
            "news_data_quality": "unknown",
            "overall_data_quality": "unknown"
        }
        
        # Assess SpaceX data quality
        spacex_data = results.get("spacex_data", {})
        if spacex_data:
            has_mission = bool(spacex_data.get("mission_name"))
            has_location = bool(spacex_data.get("launch_location"))
            has_timeline = bool(spacex_data.get("timeline"))
            
            spacex_score = sum([has_mission, has_location, has_timeline]) / 3
            quality_assessment["spacex_data_quality"] = "high" if spacex_score >= 0.8 else "medium" if spacex_score >= 0.5 else "low"
        
        # Assess weather data quality
        weather_data = results.get("weather_data", {})
        if weather_data:
            has_current = bool(weather_data.get("current_weather"))
            has_analysis = bool(weather_data.get("launch_analysis"))
            has_location = bool(weather_data.get("location"))
            
            weather_score = sum([has_current, has_analysis, has_location]) / 3
            quality_assessment["weather_data_quality"] = "high" if weather_score >= 0.8 else "medium" if weather_score >= 0.5 else "low"
        
        # Assess news data quality
        news_data = results.get("news_data", {})
        if news_data:
            has_articles = bool(news_data.get("search_results", {}).get("relevant_articles", 0) > 0)
            has_sentiment = bool(news_data.get("sentiment_analysis"))
            has_analysis = bool(news_data.get("media_risk_assessment"))
            
            news_score = sum([has_articles, has_sentiment, has_analysis]) / 3
            quality_assessment["news_data_quality"] = "high" if news_score >= 0.8 else "medium" if news_score >= 0.5 else "low"
        
        # Overall assessment
        quality_scores = [q for q in quality_assessment.values() if q != "unknown"]
        if quality_scores:
            high_count = quality_scores.count("high")
            medium_count = quality_scores.count("medium")
            
            if high_count >= len(quality_scores) * 0.7:
                quality_assessment["overall_data_quality"] = "high"
            elif high_count + medium_count >= len(quality_scores) * 0.7:
                quality_assessment["overall_data_quality"] = "medium"
            else:
                quality_assessment["overall_data_quality"] = "low"
        
        return quality_assessment
    
    def _assess_recommendations_quality(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Assess the quality of recommendations provided."""
        final_assessment = results.get("final_assessment", {})
        if isinstance(final_assessment, str):
            try:
                final_assessment = json.loads(final_assessment)
            except:
                return {"quality": "low", "reason": "Could not parse final assessment"}
        
        recommendations = final_assessment.get("recommendations", {})
        if not recommendations:
            return {"quality": "low", "reason": "No recommendations provided"}
        
        # Check for key recommendation components
        has_immediate_actions = bool(recommendations.get("immediate_actions"))
        has_monitoring_points = bool(recommendations.get("monitoring_points"))
        has_decision_timeline = bool(recommendations.get("decision_timeline"))
        
        quality_score = sum([has_immediate_actions, has_monitoring_points, has_decision_timeline]) / 3
        
        if quality_score >= 0.8:
            quality = "high"
        elif quality_score >= 0.5:
            quality = "medium"
        else:
            quality = "low"
        
        return {
            "quality": quality,
            "score": quality_score,
            "has_immediate_actions": has_immediate_actions,
            "has_monitoring_points": has_monitoring_points,
            "has_decision_timeline": has_decision_timeline
        }
    
    async def run_full_evaluation(self) -> Dict[str, Any]:
        """Run evaluation on all test goals."""
        print("🧪 Starting Goal Satisfaction Evaluation")
        print("=" * 80)
        
        evaluations = []
        total_satisfaction = 0
        
        for goal_config in self.test_goals:
            evaluation = await self.evaluate_goal_satisfaction(goal_config)
            evaluations.append(evaluation)
            total_satisfaction += evaluation.get("overall_satisfaction", 0)
        
        average_satisfaction = total_satisfaction / len(self.test_goals)
        
        summary = {
            "evaluation_type": "Goal Satisfaction",
            "total_goals_tested": len(self.test_goals),
            "average_satisfaction": average_satisfaction,
            "individual_evaluations": evaluations,
            "overall_grade": "A" if average_satisfaction >= 0.9 else "B" if average_satisfaction >= 0.7 else "C" if average_satisfaction >= 0.5 else "D",
            "recommendations": self._generate_improvement_recommendations(evaluations)
        }
        
        print(f"\n🏆 EVALUATION SUMMARY")
        print(f"Average Satisfaction: {average_satisfaction:.2%}")
        print(f"Overall Grade: {summary['overall_grade']}")
        
        return summary
    
    def _generate_improvement_recommendations(self, evaluations: List[Dict[str, Any]]) -> List[str]:
        """Generate recommendations for improving the system."""
        recommendations = []
        
        # Analyze common failure patterns
        low_satisfaction_goals = [e for e in evaluations if e.get("overall_satisfaction", 0) < 0.7]
        
        if low_satisfaction_goals:
            recommendations.append("Improve agent coordination for complex multi-step goals")
        
        # Check data quality issues
        data_quality_issues = []
        for evaluation in evaluations:
            data_quality = evaluation.get("data_quality", {})
            for component, quality in data_quality.items():
                if quality == "low":
                    data_quality_issues.append(component)
        
        if data_quality_issues:
            recommendations.append(f"Improve data quality for: {', '.join(set(data_quality_issues))}")
        
        # Check for iteration issues
        high_iteration_goals = [e for e in evaluations if e.get("iterations_used", 0) > 1]
        if high_iteration_goals:
            recommendations.append("Optimize initial planning to reduce need for iterations")
        
        return recommendations


async def main():
    """Run the goal satisfaction evaluation."""
    if not settings.validate():
        print("❌ Configuration validation failed. Please check your .env file.")
        return
    
    evaluator = GoalSatisfactionEvaluator()
    results = await evaluator.run_full_evaluation()
    
    # Save results
    with open("goal_satisfaction_evaluation.json", "w") as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📄 Detailed results saved to goal_satisfaction_evaluation.json")


if __name__ == "__main__":
    asyncio.run(main())
