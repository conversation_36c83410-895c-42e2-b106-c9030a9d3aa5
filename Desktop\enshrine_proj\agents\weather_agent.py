"""Weather Agent - Fetches and analyzes weather data for launch locations."""

from google.adk.agents import LlmAgent
from google.adk.tools import FunctionTool
from tools.weather_api import fetch_weather_for_location, fetch_weather_forecast_for_location
from typing import Dict, Any
import json


class WeatherAgent:
    """
    The Weather Agent specializes in fetching weather data and analyzing launch conditions.
    It enriches SpaceX data with weather forecasts and launch suitability analysis.
    """
    
    def __init__(self, model: str = "gemini-2.0-flash"):
        self.model = model
        self.agent = self._create_agent()
    
    def _create_agent(self) -> LlmAgent:
        """Create the Weather LLM agent with appropriate tools and instructions."""
        
        instruction = """
        You are a specialized weather analysis agent responsible for fetching weather data and analyzing launch conditions.
        
        Your capabilities include:
        1. Fetching current weather for specific coordinates
        2. Getting weather forecasts for launch locations
        3. Analyzing weather conditions for launch suitability
        4. Identifying potential weather-related delays
        5. Providing detailed meteorological context
        
        When analyzing weather for launches, consider:
        - Wind speed and direction (critical for launch safety)
        - Precipitation (rain, snow, storms)
        - Cloud coverage and visibility
        - Temperature extremes
        - Atmospheric pressure
        - Lightning risk
        - Sea state (for ocean landings)
        
        Launch weather criteria (general guidelines):
        - Wind speed: Usually < 15 m/s (33 mph)
        - No precipitation during launch window
        - Cloud coverage: Preferably < 80%
        - Visibility: > 5 km
        - No thunderstorms within 10 nautical miles
        - Temperature: Within operational limits
        
        Always read launch location data from state['spacex_data'] if available.
        Save your weather analysis to state['weather_data'] with this structure:
        {
            "location": {
                "name": "Location name",
                "coordinates": {"lat": float, "lon": float}
            },
            "current_weather": {
                "temperature": float,
                "description": "Weather description",
                "wind_speed": float,
                "humidity": float,
                "pressure": float
            },
            "forecast": [...],
            "launch_analysis": {
                "suitable": boolean,
                "risk_level": "low|medium|high",
                "issues": ["list of potential issues"],
                "recommendations": "Weather-based recommendations"
            },
            "enrichment_notes": "Additional meteorological context"
        }
        
        If weather data suggests potential delays, provide specific reasoning and timeframe estimates.
        """
        
        tools = [
            FunctionTool(func=fetch_weather_for_location),
            FunctionTool(func=fetch_weather_forecast_for_location),
            FunctionTool(func=self._analyze_launch_weather),
            FunctionTool(func=self._extract_coordinates_from_spacex_data),
            FunctionTool(func=self._assess_weather_trends)
        ]
        
        return LlmAgent(
            name="WeatherAgent",
            model=self.model,
            instruction=instruction,
            description="Analyzes weather conditions and forecasts for launch locations",
            tools=tools,
            output_key="weather_data"
        )
    
    def _extract_coordinates_from_spacex_data(self, spacex_data: str) -> str:
        """Extract coordinates from SpaceX data for weather lookup."""
        try:
            if isinstance(spacex_data, str):
                data = json.loads(spacex_data)
            else:
                data = spacex_data
            
            if "launch_location" in data and "coordinates" in data["launch_location"]:
                coords = data["launch_location"]["coordinates"]
                lat = coords.get("lat")
                lon = coords.get("lon")
                
                if lat is not None and lon is not None:
                    return json.dumps({
                        "latitude": lat,
                        "longitude": lon,
                        "location_name": data["launch_location"].get("name", ""),
                        "ready_for_weather_fetch": True
                    })
                else:
                    return "Coordinates not available in SpaceX data"
            else:
                return "No launch location found in SpaceX data"
                
        except Exception as e:
            return f"Error extracting coordinates: {str(e)}"
    
    def _analyze_launch_weather(self, weather_data: str, launch_timeline: str = "") -> str:
        """Analyze weather conditions specifically for launch suitability."""
        try:
            weather = json.loads(weather_data) if isinstance(weather_data, str) else weather_data
            
            if not weather:
                return "No weather data available for analysis"
            
            # Extract current weather and forecast
            current = weather.get("weather", {}).get("current", {})
            launch_analysis = weather.get("launch_analysis", {})
            
            # Enhanced analysis
            risk_factors = []
            recommendations = []
            
            # Wind analysis
            wind_speed = current.get("temperature", 0)  # This seems wrong in the original API
            if "wind" in weather.get("weather", {}):
                wind_speed = weather["weather"]["wind"].get("speed", 0)
            
            if wind_speed > 15:
                risk_factors.append(f"High wind speed: {wind_speed} m/s (limit: ~15 m/s)")
                recommendations.append("Monitor wind conditions closely; consider delay if winds persist")
            elif wind_speed > 10:
                risk_factors.append(f"Moderate wind speed: {wind_speed} m/s")
                recommendations.append("Acceptable but monitor for increases")
            
            # Precipitation analysis
            precipitation = weather.get("weather", {}).get("precipitation", {})
            rain = precipitation.get("rain_1h", 0)
            snow = precipitation.get("snow_1h", 0)
            
            if rain > 0 or snow > 0:
                risk_factors.append(f"Precipitation detected: rain={rain}mm, snow={snow}mm")
                recommendations.append("Wait for precipitation to clear before launch")
            
            # Cloud coverage
            clouds = weather.get("weather", {}).get("clouds", {}).get("coverage", 0)
            if clouds > 80:
                risk_factors.append(f"Heavy cloud coverage: {clouds}%")
                recommendations.append("Monitor for clearing; may affect visibility")
            
            # Determine overall risk level
            if len(risk_factors) == 0:
                risk_level = "low"
                overall_suitable = True
            elif len(risk_factors) <= 2 and wind_speed < 20:
                risk_level = "medium"
                overall_suitable = True
            else:
                risk_level = "high"
                overall_suitable = False
            
            analysis = {
                "suitable_for_launch": overall_suitable,
                "risk_level": risk_level,
                "risk_factors": risk_factors,
                "recommendations": recommendations,
                "weather_summary": {
                    "description": current.get("description", ""),
                    "temperature": current.get("temperature", 0),
                    "wind_speed": wind_speed,
                    "clouds": clouds,
                    "precipitation": rain + snow
                },
                "confidence": "high" if weather else "low"
            }
            
            return json.dumps(analysis, indent=2)
            
        except Exception as e:
            return f"Error analyzing launch weather: {str(e)}"
    
    def _assess_weather_trends(self, forecast_data: str) -> str:
        """Assess weather trends over the forecast period."""
        try:
            forecast = json.loads(forecast_data) if isinstance(forecast_data, str) else forecast_data
            
            if not forecast or "forecast" not in forecast:
                return "No forecast data available for trend analysis"
            
            forecasts = forecast["forecast"]
            if not forecasts:
                return "Empty forecast data"
            
            # Analyze trends
            wind_speeds = []
            temperatures = []
            precipitation_probs = []
            
            for item in forecasts[:8]:  # Next 24 hours (3-hour intervals)
                wind_speeds.append(item.get("wind_speed", 0))
                temperatures.append(item.get("temperature", 0))
                precipitation_probs.append(item.get("precipitation_probability", 0))
            
            trends = {
                "wind_trend": "increasing" if wind_speeds[-1] > wind_speeds[0] else "decreasing",
                "max_wind_speed": max(wind_speeds),
                "avg_wind_speed": sum(wind_speeds) / len(wind_speeds),
                "temperature_range": {"min": min(temperatures), "max": max(temperatures)},
                "precipitation_risk": max(precipitation_probs),
                "improving": wind_speeds[-1] < wind_speeds[0] and precipitation_probs[-1] < precipitation_probs[0],
                "forecast_period": "24 hours"
            }
            
            return json.dumps(trends, indent=2)
            
        except Exception as e:
            return f"Error assessing weather trends: {str(e)}"
    
    def get_agent(self) -> LlmAgent:
        """Get the configured LLM agent."""
        return self.agent


def create_weather_agent(model: str = "gemini-2.0-flash") -> LlmAgent:
    """Factory function to create a weather agent."""
    weather_agent = WeatherAgent(model)
    return weather_agent.get_agent()
