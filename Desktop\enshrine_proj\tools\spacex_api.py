"""SpaceX API integration tool."""

import asyncio
import httpx
from typing import Dict, List, Optional, Any
from datetime import datetime
from config.settings import settings


class SpaceXAPI:
    """SpaceX API client for fetching launch data."""
    
    def __init__(self):
        self.base_url = settings.SPACEX_API_URL
        self.timeout = settings.TIMEOUT_SECONDS
    
    async def get_next_launch(self) -> Optional[Dict[str, Any]]:
        """Get the next upcoming SpaceX launch."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(f"{self.base_url}/launches/next")
                response.raise_for_status()
                return response.json()
        except Exception as e:
            print(f"Error fetching next launch: {e}")
            return None
    
    async def get_upcoming_launches(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get upcoming SpaceX launches."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(f"{self.base_url}/launches/upcoming")
                response.raise_for_status()
                launches = response.json()
                return launches[:limit]
        except Exception as e:
            print(f"Error fetching upcoming launches: {e}")
            return []
    
    async def get_launch_pad_info(self, launchpad_id: str) -> Optional[Dict[str, Any]]:
        """Get information about a specific launch pad."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(f"{self.base_url}/launchpads/{launchpad_id}")
                response.raise_for_status()
                return response.json()
        except Exception as e:
            print(f"Error fetching launchpad info: {e}")
            return None
    
    def format_launch_data(self, launch_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format launch data for easier consumption by agents."""
        if not launch_data:
            return {}
        
        formatted = {
            "mission_name": launch_data.get("name", "Unknown"),
            "launch_date": launch_data.get("date_utc", ""),
            "launch_date_local": launch_data.get("date_local", ""),
            "flight_number": launch_data.get("flight_number", 0),
            "details": launch_data.get("details", ""),
            "success": launch_data.get("success"),
            "upcoming": launch_data.get("upcoming", False),
            "launchpad_id": launch_data.get("launchpad", ""),
            "rocket_id": launch_data.get("rocket", ""),
            "links": {
                "webcast": launch_data.get("links", {}).get("webcast", ""),
                "article": launch_data.get("links", {}).get("article", ""),
                "wikipedia": launch_data.get("links", {}).get("wikipedia", "")
            }
        }
        
        # Parse date for easier handling
        if formatted["launch_date"]:
            try:
                dt = datetime.fromisoformat(formatted["launch_date"].replace('Z', '+00:00'))
                formatted["launch_timestamp"] = dt.timestamp()
                formatted["launch_date_readable"] = dt.strftime("%Y-%m-%d %H:%M:%S UTC")
            except:
                formatted["launch_timestamp"] = None
                formatted["launch_date_readable"] = formatted["launch_date"]
        
        return formatted


# Function tools for ADK integration
async def fetch_next_spacex_launch() -> str:
    """Fetch the next SpaceX launch data."""
    api = SpaceXAPI()
    launch_data = await api.get_next_launch()
    
    if not launch_data:
        return "Failed to fetch SpaceX launch data"
    
    formatted_data = api.format_launch_data(launch_data)
    
    # Get launchpad info if available
    if formatted_data.get("launchpad_id"):
        launchpad_info = await api.get_launch_pad_info(formatted_data["launchpad_id"])
        if launchpad_info:
            formatted_data["launchpad_info"] = {
                "name": launchpad_info.get("name", ""),
                "full_name": launchpad_info.get("full_name", ""),
                "locality": launchpad_info.get("locality", ""),
                "region": launchpad_info.get("region", ""),
                "latitude": launchpad_info.get("latitude"),
                "longitude": launchpad_info.get("longitude"),
                "status": launchpad_info.get("status", "")
            }
    
    return str(formatted_data)


async def fetch_upcoming_spacex_launches(limit: int = 3) -> str:
    """Fetch upcoming SpaceX launches."""
    api = SpaceXAPI()
    launches = await api.get_upcoming_launches(limit)
    
    if not launches:
        return "Failed to fetch upcoming SpaceX launches"
    
    formatted_launches = [api.format_launch_data(launch) for launch in launches]
    return str(formatted_launches)
