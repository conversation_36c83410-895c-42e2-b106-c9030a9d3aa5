"""SpaceX Agent - Fetches and processes SpaceX launch data."""

from google.adk.agents import LlmAgent
from google.adk.tools import FunctionTool
from tools.spacex_api import fetch_next_spacex_launch, fetch_upcoming_spacex_launches
from typing import Dict, Any
import json


class SpaceXAgent:
    """
    The SpaceX Agent specializes in fetching and processing SpaceX launch data.
    It enriches the data with launch pad information and mission details.
    """
    
    def __init__(self, model: str = "gemini-2.0-flash"):
        self.model = model
        self.agent = self._create_agent()
    
    def _create_agent(self) -> LlmAgent:
        """Create the SpaceX LLM agent with appropriate tools and instructions."""
        
        instruction = """
        You are a specialized SpaceX data agent responsible for fetching and processing SpaceX launch information.
        
        Your capabilities include:
        1. Fetching next upcoming SpaceX launch data
        2. Getting multiple upcoming launches
        3. Extracting launch pad location information
        4. Processing mission details and timeline
        5. Enriching data with relevant context
        
        When processing launch data, always:
        - Extract key information: mission name, launch date, location coordinates
        - Include launch pad details (name, location, coordinates)
        - Provide mission context and details
        - Calculate time until launch
        - Identify any special mission characteristics
        
        Data enrichment priorities:
        1. Launch location coordinates (essential for weather analysis)
        2. Mission timeline and criticality
        3. Launch pad status and capabilities
        4. Historical context if relevant
        
        Always save your processed data to state['spacex_data'] with this structure:
        {
            "mission_name": "Mission name",
            "launch_date": "ISO date string",
            "launch_location": {
                "name": "Launch pad name",
                "coordinates": {"lat": float, "lon": float},
                "region": "Location region"
            },
            "mission_details": "Mission description",
            "timeline": "Time until launch",
            "criticality": "Mission importance level",
            "enrichment_notes": "Additional context"
        }
        
        If you cannot fetch data, provide clear error information and suggest alternatives.
        """
        
        tools = [
            FunctionTool(func=fetch_next_spacex_launch),
            FunctionTool(func=fetch_upcoming_spacex_launches),
            FunctionTool(func=self._process_launch_data),
            FunctionTool(func=self._extract_location_data),
            FunctionTool(func=self._calculate_launch_timeline)
        ]
        
        return LlmAgent(
            name="SpaceXAgent",
            model=self.model,
            instruction=instruction,
            description="Fetches and processes SpaceX launch data with location and mission details",
            tools=tools,
            output_key="spacex_data"
        )
    
    def _process_launch_data(self, raw_launch_data: str) -> str:
        """Process raw launch data and extract key information."""
        try:
            # Parse the raw data (it comes as a string representation)
            launch_data = eval(raw_launch_data) if isinstance(raw_launch_data, str) else raw_launch_data
            
            if not launch_data:
                return "No launch data available to process"
            
            processed = {
                "mission_name": launch_data.get("mission_name", "Unknown Mission"),
                "launch_date": launch_data.get("launch_date_readable", ""),
                "flight_number": launch_data.get("flight_number", 0),
                "mission_details": launch_data.get("details", "No details available"),
                "status": "upcoming" if launch_data.get("upcoming") else "completed",
                "links": launch_data.get("links", {}),
                "raw_data": launch_data
            }
            
            # Extract location if available
            if "launchpad_info" in launch_data:
                launchpad = launch_data["launchpad_info"]
                processed["launch_location"] = {
                    "name": launchpad.get("full_name", launchpad.get("name", "")),
                    "coordinates": {
                        "lat": launchpad.get("latitude"),
                        "lon": launchpad.get("longitude")
                    },
                    "region": f"{launchpad.get('locality', '')}, {launchpad.get('region', '')}",
                    "status": launchpad.get("status", "")
                }
            
            return json.dumps(processed, indent=2)
            
        except Exception as e:
            return f"Error processing launch data: {str(e)}"
    
    def _extract_location_data(self, launch_data: str) -> str:
        """Extract location coordinates for weather analysis."""
        try:
            data = json.loads(launch_data) if isinstance(launch_data, str) else launch_data
            
            if "launch_location" in data and "coordinates" in data["launch_location"]:
                coords = data["launch_location"]["coordinates"]
                location_info = {
                    "latitude": coords.get("lat"),
                    "longitude": coords.get("lon"),
                    "location_name": data["launch_location"].get("name", ""),
                    "region": data["launch_location"].get("region", ""),
                    "ready_for_weather_check": coords.get("lat") is not None and coords.get("lon") is not None
                }
                return json.dumps(location_info)
            else:
                return "No location coordinates available in launch data"
                
        except Exception as e:
            return f"Error extracting location data: {str(e)}"
    
    def _calculate_launch_timeline(self, launch_date: str) -> str:
        """Calculate time until launch and provide timeline context."""
        try:
            from datetime import datetime
            
            if not launch_date:
                return "No launch date available"
            
            # Parse the launch date
            try:
                launch_dt = datetime.fromisoformat(launch_date.replace('Z', '+00:00'))
                now = datetime.now(launch_dt.tzinfo)
                time_diff = launch_dt - now
                
                if time_diff.total_seconds() > 0:
                    days = time_diff.days
                    hours = time_diff.seconds // 3600
                    minutes = (time_diff.seconds % 3600) // 60
                    
                    timeline = {
                        "days_until_launch": days,
                        "hours_until_launch": hours,
                        "minutes_until_launch": minutes,
                        "total_hours": time_diff.total_seconds() / 3600,
                        "status": "upcoming",
                        "urgency": "high" if days < 1 else "medium" if days < 7 else "low",
                        "weather_check_priority": "critical" if days < 3 else "important" if days < 7 else "normal"
                    }
                else:
                    timeline = {
                        "status": "past",
                        "message": "Launch date has already passed"
                    }
                
                return json.dumps(timeline)
                
            except ValueError:
                return f"Could not parse launch date: {launch_date}"
                
        except Exception as e:
            return f"Error calculating timeline: {str(e)}"
    
    def get_agent(self) -> LlmAgent:
        """Get the configured LLM agent."""
        return self.agent


def create_spacex_agent(model: str = "gemini-2.0-flash") -> LlmAgent:
    """Factory function to create a SpaceX agent."""
    spacex_agent = SpaceXAgent(model)
    return spacex_agent.get_agent()
