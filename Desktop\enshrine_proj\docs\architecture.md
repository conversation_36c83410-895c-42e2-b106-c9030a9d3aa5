# Multi-Agent System Architecture

## Overview

This multi-agent system demonstrates sophisticated AI collaboration using Google's Agent Development Kit (ADK). The system takes complex user goals and achieves them through coordinated agent interactions, data enrichment, and iterative refinement.

## System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Goal     │───▶│ Planner Agent   │───▶│ Execution Plan  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Sequential Workflow                          │
├─────────────────┬─────────────────┬─────────────────┬───────────┤
│  SpaceX Agent   │ Weather Agent   │  News Agent     │Coordinator│
│                 │                 │                 │   Agent   │
│ • Fetch launch  │ • Get weather   │ • Search news   │• Synthesize│
│   data          │   for location  │   articles      │  all data │
│ • Extract       │ • Analyze       │ • Sentiment     │• Assess   │
│   location      │   conditions    │   analysis      │  delays   │
│ • Mission info  │ • Launch        │ • Delay         │• Generate │
│                 │   suitability   │   indicators    │  report   │
└─────────────────┴─────────────────┴─────────────────┴───────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Data Enrichment Flow                         │
│                                                                 │
│ Raw Goal ──▶ Launch Data ──▶ Weather Analysis ──▶ News Context │
│                     │              │                    │      │
│                     ▼              ▼                    ▼      │
│              Location Info ──▶ Risk Assessment ──▶ Final Report│
└─────────────────────────────────────────────────────────────────┘
```

## Agent Responsibilities

### 1. Planner Agent
- **Purpose**: Analyzes user goals and creates detailed execution plans
- **Capabilities**:
  - Goal decomposition into actionable steps
  - Agent routing and task assignment
  - Success criteria definition
  - Plan validation and optimization
- **Output**: Structured execution plan stored in session state

### 2. SpaceX Agent
- **Purpose**: Fetches and processes SpaceX launch data
- **Capabilities**:
  - Next launch data retrieval
  - Launch pad location extraction
  - Mission timeline calculation
  - Data enrichment with context
- **APIs Used**: SpaceX API (free, no key required)
- **Output**: Enriched launch data with location coordinates

### 3. Weather Agent
- **Purpose**: Analyzes weather conditions for launch locations
- **Capabilities**:
  - Current weather fetching
  - Weather forecast analysis
  - Launch suitability assessment
  - Risk factor identification
- **APIs Used**: OpenWeatherMap API
- **Dependencies**: Requires location data from SpaceX Agent
- **Output**: Weather analysis with launch recommendations

### 4. News Agent
- **Purpose**: Searches and analyzes mission-related news
- **Capabilities**:
  - Mission-specific news search
  - Sentiment analysis
  - Delay indicator detection
  - Media risk assessment
- **APIs Used**: NewsAPI
- **Dependencies**: Requires mission name from SpaceX Agent
- **Output**: News sentiment analysis with delay indicators

### 5. Coordinator Agent
- **Purpose**: Synthesizes all data and provides final assessment
- **Capabilities**:
  - Multi-source data synthesis
  - Risk assessment calculation
  - Recommendation generation
  - Goal achievement evaluation
- **Dependencies**: Requires data from all previous agents
- **Output**: Final assessment with delay probability and recommendations

## Data Flow Architecture

### Sequential Processing
The system uses ADK's `SequentialAgent` to ensure proper data flow:

1. **Planner** → Creates execution roadmap
2. **SpaceX** → Provides launch data and location
3. **Weather** → Enriches with weather analysis (uses SpaceX location)
4. **News** → Enriches with media sentiment (uses SpaceX mission name)
5. **Coordinator** → Synthesizes all data into final assessment

### Data Dependencies
```
SpaceX Data
├── mission_name ──────────────┐
├── launch_date               │
├── location_coordinates ──┐   │
└── mission_details        │   │
                          │   │
Weather Analysis          │   │
├── current_conditions ◄──┘   │
├── forecast_data             │
├── launch_suitability        │
└── risk_assessment           │
                              │
News Analysis                 │
├── article_search ◄──────────┘
├── sentiment_analysis
├── delay_indicators
└── media_risk_assessment
                │
                ▼
Final Assessment
├── delay_probability
├── risk_factors
├── recommendations
└── confidence_score
```

## Communication Mechanisms

### 1. Shared Session State
- **Primary Method**: Agents communicate through `session.state`
- **Pattern**: Each agent writes to specific keys (e.g., `spacex_data`, `weather_data`)
- **Benefits**: Persistent data across agent executions, easy debugging

### 2. Output Keys
- **Mechanism**: ADK's `output_key` parameter automatically saves agent responses
- **Usage**: `LlmAgent(output_key="spacex_data")` saves response to `state['spacex_data']`
- **Benefits**: Automatic data persistence, consistent naming

### 3. Tool-based Communication
- **Method**: Agents use function tools to process and enrich data
- **Examples**: 
  - `extract_coordinates_from_spacex_data()`
  - `analyze_launch_weather()`
  - `assess_media_risk()`

## Iterative Refinement

### Goal Achievement Monitoring
The system monitors goal achievement through:
- **Success Criteria**: Defined in execution plan
- **Completeness Scoring**: Percentage of goal achieved
- **Quality Assessment**: Data quality and recommendation usefulness

### Iteration Logic
```python
if not goal_achieved and iterations < max_iterations:
    # Re-run workflow with refined context
    # Update session state with previous attempt data
    # Provide more specific instructions to agents
```

### Refinement Strategies
1. **Data Gap Filling**: Identify missing information and re-fetch
2. **Analysis Deepening**: Provide more detailed analysis of existing data
3. **Context Enhancement**: Add more context to improve decision-making

## Error Handling and Resilience

### API Failure Handling
- **Graceful Degradation**: System continues with available data
- **Fallback Strategies**: Alternative data sources or cached data
- **Error Reporting**: Clear error messages with suggested actions

### Agent Failure Recovery
- **Isolation**: Agent failures don't crash entire system
- **Retry Logic**: Automatic retry for transient failures
- **Partial Success**: System provides results even with some agent failures

## Scalability Considerations

### Horizontal Scaling
- **Agent Parallelization**: Weather and News agents can run in parallel
- **API Rate Limiting**: Built-in respect for API limits
- **Resource Management**: Configurable timeouts and concurrency limits

### Vertical Scaling
- **Model Selection**: Configurable LLM models per agent
- **Memory Management**: Efficient session state management
- **Performance Monitoring**: Built-in execution time tracking

## Security and Privacy

### API Key Management
- **Environment Variables**: All API keys stored in `.env` file
- **Key Rotation**: Easy key updates without code changes
- **Minimal Permissions**: APIs used with minimal required permissions

### Data Privacy
- **No Persistent Storage**: Session data cleared after execution
- **API Data**: Only public APIs used, no sensitive data
- **Logging**: Configurable logging levels for debugging vs. production

## Extension Points

### Adding New Agents
1. Create agent class inheriting from appropriate ADK base
2. Define tools and capabilities
3. Add to workflow sequence
4. Update data flow documentation

### Adding New APIs
1. Create API client in `tools/` directory
2. Implement function tools for agent integration
3. Add configuration to `settings.py`
4. Update agent capabilities

### Custom Workflows
- **Parallel Execution**: Use `ParallelAgent` for independent tasks
- **Conditional Logic**: Use `LoopAgent` for iterative processes
- **Custom Orchestration**: Implement custom workflow agents

This architecture provides a robust, scalable foundation for multi-agent AI systems with clear separation of concerns, efficient data flow, and comprehensive error handling.
