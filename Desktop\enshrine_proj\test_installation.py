#!/usr/bin/env python3
"""
Installation Test Script

This script verifies that all components of the multi-agent system are properly installed
and configured. Run this before using the main system.
"""

import sys
import os
import asyncio
from typing import Dict, Any, List

def test_python_version():
    """Test Python version compatibility."""
    print("🐍 Testing Python version...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"   ✅ Python {version.major}.{version.minor}.{version.micro} (Compatible)")
        return True
    else:
        print(f"   ❌ Python {version.major}.{version.minor}.{version.micro} (Requires Python 3.8+)")
        return False

def test_dependencies():
    """Test required package imports."""
    print("\n📦 Testing package dependencies...")
    
    required_packages = [
        ("google.adk", "Google ADK"),
        ("httpx", "HTTPX HTTP client"),
        ("pydantic", "Pydantic data validation"),
        ("dotenv", "Python-dotenv"),
        ("asyncio", "AsyncIO (built-in)"),
        ("json", "JSON (built-in)"),
        ("typing", "Typing (built-in)")
    ]
    
    success_count = 0
    for package, description in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {description}")
            success_count += 1
        except ImportError as e:
            print(f"   ❌ {description} - {str(e)}")
    
    return success_count == len(required_packages)

def test_configuration():
    """Test configuration and environment setup."""
    print("\n⚙️ Testing configuration...")
    
    try:
        from config.settings import settings
        
        # Test configuration loading
        print(f"   ✅ Configuration module loaded")
        
        # Test environment validation
        is_valid = settings.validate()
        if is_valid:
            print(f"   ✅ Environment configuration valid")
        else:
            print(f"   ⚠️ Environment configuration incomplete (some API keys missing)")
            print(f"      This is normal if you haven't set up all APIs yet")
        
        # Test individual settings
        config_status = []
        config_status.append(("Google API Key", bool(settings.GOOGLE_API_KEY)))
        config_status.append(("OpenWeather API Key", bool(settings.OPENWEATHER_API_KEY)))
        config_status.append(("News API Key", bool(settings.NEWS_API_KEY)))
        config_status.append(("Max Iterations", settings.MAX_ITERATIONS > 0))
        config_status.append(("Timeout Settings", settings.TIMEOUT_SECONDS > 0))
        
        for setting_name, is_configured in config_status:
            status = "✅" if is_configured else "⚠️"
            print(f"   {status} {setting_name}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Configuration error: {str(e)}")
        return False

def test_project_structure():
    """Test project file structure."""
    print("\n📁 Testing project structure...")
    
    required_files = [
        "agents/__init__.py",
        "agents/planner_agent.py",
        "agents/spacex_agent.py", 
        "agents/weather_agent.py",
        "agents/news_agent.py",
        "agents/coordinator_agent.py",
        "tools/__init__.py",
        "tools/spacex_api.py",
        "tools/weather_api.py",
        "tools/news_api.py",
        "config/__init__.py",
        "config/settings.py",
        "evaluations/__init__.py",
        "evaluations/test_goal_satisfaction.py",
        "evaluations/test_agent_trajectory.py",
        "main.py",
        "requirements.txt",
        ".env.example"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n   ⚠️ Missing files: {', '.join(missing_files)}")
        return False
    
    return True

def test_agent_imports():
    """Test agent module imports."""
    print("\n🤖 Testing agent imports...")
    
    agents = [
        ("agents.planner_agent", "Planner Agent"),
        ("agents.spacex_agent", "SpaceX Agent"),
        ("agents.weather_agent", "Weather Agent"),
        ("agents.news_agent", "News Agent"),
        ("agents.coordinator_agent", "Coordinator Agent")
    ]
    
    success_count = 0
    for module, description in agents:
        try:
            __import__(module)
            print(f"   ✅ {description}")
            success_count += 1
        except Exception as e:
            print(f"   ❌ {description} - {str(e)}")
    
    return success_count == len(agents)

def test_tool_imports():
    """Test tool module imports."""
    print("\n🔧 Testing tool imports...")
    
    tools = [
        ("tools.spacex_api", "SpaceX API Tool"),
        ("tools.weather_api", "Weather API Tool"),
        ("tools.news_api", "News API Tool")
    ]
    
    success_count = 0
    for module, description in tools:
        try:
            __import__(module)
            print(f"   ✅ {description}")
            success_count += 1
        except Exception as e:
            print(f"   ❌ {description} - {str(e)}")
    
    return success_count == len(tools)

async def test_api_connectivity():
    """Test basic API connectivity (without requiring API keys)."""
    print("\n🌐 Testing API connectivity...")
    
    try:
        import httpx
        
        # Test SpaceX API (no key required)
        try:
            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.get("https://api.spacexdata.com/v4/launches/latest")
                if response.status_code == 200:
                    print(f"   ✅ SpaceX API accessible")
                else:
                    print(f"   ⚠️ SpaceX API returned status {response.status_code}")
        except Exception as e:
            print(f"   ❌ SpaceX API connection failed: {str(e)}")
        
        # Test other APIs (just connectivity, not authentication)
        apis_to_test = [
            ("https://api.openweathermap.org", "OpenWeatherMap API"),
            ("https://newsapi.org", "NewsAPI")
        ]
        
        for url, name in apis_to_test:
            try:
                async with httpx.AsyncClient(timeout=10) as client:
                    response = await client.get(url)
                    print(f"   ✅ {name} accessible (status: {response.status_code})")
            except Exception as e:
                print(f"   ⚠️ {name} connection issue: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ API connectivity test failed: {str(e)}")
        return False

def test_main_system():
    """Test main system import."""
    print("\n🚀 Testing main system...")
    
    try:
        from main import MultiAgentSystem
        system = MultiAgentSystem()
        print(f"   ✅ MultiAgentSystem created successfully")
        print(f"   ✅ {len(system.agents)} agents configured")
        return True
    except Exception as e:
        print(f"   ❌ Main system test failed: {str(e)}")
        return False

async def run_all_tests():
    """Run all installation tests."""
    print("🧪 Multi-Agent System Installation Test")
    print("=" * 50)
    
    tests = [
        ("Python Version", test_python_version),
        ("Dependencies", test_dependencies),
        ("Configuration", test_configuration),
        ("Project Structure", test_project_structure),
        ("Agent Imports", test_agent_imports),
        ("Tool Imports", test_tool_imports),
        ("API Connectivity", test_api_connectivity),
        ("Main System", test_main_system)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ {test_name} test crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("-" * 30)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Your installation is ready.")
        print("\nNext steps:")
        print("1. Set up your API keys in .env file (see docs/api_setup.md)")
        print("2. Run: python main.py")
    else:
        print(f"\n⚠️ {total - passed} tests failed. Please check the errors above.")
        print("\nTroubleshooting:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Check Python version (requires 3.8+)")
        print("3. Verify project structure is complete")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(run_all_tests())
