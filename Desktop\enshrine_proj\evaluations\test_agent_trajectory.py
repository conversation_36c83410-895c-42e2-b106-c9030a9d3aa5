"""
Agent Trajectory Evaluation

This module tests the agent chaining, data enrichment flow, and iterative refinement
capabilities of the multi-agent system.
"""

import asyncio
import json
from typing import Dict, Any, List, Tuple
from main import MultiAgentSystem
from config.settings import settings


class AgentTrajectoryEvaluator:
    """Evaluates agent interaction patterns, data flow, and trajectory optimization."""
    
    def __init__(self):
        self.system = MultiAgentSystem()
        self.trajectory_tests = [
            {
                "name": "Sequential Data Enrichment",
                "goal": "Find next SpaceX launch and analyze all factors for potential delays",
                "expected_trajectory": ["planner", "spacex", "weather", "news", "coordinator"],
                "data_flow_checks": [
                    ("spacex_data", "weather_agent_input"),
                    ("spacex_data", "news_agent_input"),
                    ("weather_data", "coordinator_input"),
                    ("news_data", "coordinator_input")
                ]
            },
            {
                "name": "Iterative Refinement",
                "goal": "Provide incomplete initial request to test iteration",
                "expected_behavior": "system_should_iterate",
                "max_iterations": 2
            },
            {
                "name": "Agent Dependency Resolution",
                "goal": "Test that weather agent waits for SpaceX location data",
                "dependency_checks": [
                    ("weather", "depends_on", "spacex"),
                    ("coordinator", "depends_on", ["spacex", "weather", "news"])
                ]
            }
        ]
    
    async def evaluate_agent_trajectory(self, test_config: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate a specific agent trajectory test."""
        test_name = test_config["name"]
        goal = test_config["goal"]
        
        print(f"\n🔄 Evaluating trajectory: {test_name}")
        print("-" * 60)
        
        try:
            # Execute with trajectory tracking
            results = await self._execute_with_tracking(goal)
            
            # Analyze trajectory
            trajectory_analysis = self._analyze_trajectory(results, test_config)
            
            # Evaluate data enrichment
            enrichment_analysis = self._analyze_data_enrichment(results)
            
            # Check agent dependencies
            dependency_analysis = self._analyze_dependencies(results, test_config)
            
            evaluation = {
                "test_name": test_name,
                "goal": goal,
                "trajectory_analysis": trajectory_analysis,
                "enrichment_analysis": enrichment_analysis,
                "dependency_analysis": dependency_analysis,
                "overall_score": self._calculate_trajectory_score(trajectory_analysis, enrichment_analysis, dependency_analysis),
                "execution_metadata": {
                    "iterations_used": results.get("iterations_used", 0),
                    "goal_achieved": results.get("goal_achieved", False),
                    "agents_executed": self._extract_agent_execution_order(results)
                }
            }
            
            print(f"🏆 Trajectory Score: {evaluation['overall_score']:.2%}")
            return evaluation
            
        except Exception as e:
            print(f"❌ Error evaluating trajectory: {str(e)}")
            return {
                "test_name": test_name,
                "error": str(e),
                "overall_score": 0.0
            }
    
    async def _execute_with_tracking(self, goal: str) -> Dict[str, Any]:
        """Execute goal with enhanced tracking of agent interactions."""
        # This would ideally hook into ADK's callback system for detailed tracking
        # For now, we'll use the standard execution and infer trajectory from results
        results = await self.system.execute_goal(goal)
        
        # Add trajectory tracking metadata
        results["trajectory_metadata"] = {
            "execution_order": self._extract_agent_execution_order(results),
            "data_dependencies": self._extract_data_dependencies(results),
            "enrichment_chain": self._extract_enrichment_chain(results)
        }
        
        return results
    
    def _extract_agent_execution_order(self, results: Dict[str, Any]) -> List[str]:
        """Extract the order in which agents were executed."""
        # Infer execution order from presence of data in session state
        execution_order = []
        
        if "execution_plan" in results:
            execution_order.append("planner")
        
        if "spacex_data" in results:
            execution_order.append("spacex")
        
        if "weather_data" in results:
            execution_order.append("weather")
        
        if "news_data" in results:
            execution_order.append("news")
        
        if "final_assessment" in results:
            execution_order.append("coordinator")
        
        return execution_order
    
    def _extract_data_dependencies(self, results: Dict[str, Any]) -> Dict[str, List[str]]:
        """Extract data dependencies between agents."""
        dependencies = {}
        
        # Weather agent depends on SpaceX location data
        if "weather_data" in results and "spacex_data" in results:
            dependencies["weather"] = ["spacex"]
        
        # News agent depends on SpaceX mission data
        if "news_data" in results and "spacex_data" in results:
            dependencies["news"] = ["spacex"]
        
        # Coordinator depends on all previous agents
        if "final_assessment" in results:
            coordinator_deps = []
            for agent in ["spacex", "weather", "news"]:
                if f"{agent}_data" in results:
                    coordinator_deps.append(agent)
            dependencies["coordinator"] = coordinator_deps
        
        return dependencies
    
    def _extract_enrichment_chain(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract the data enrichment chain showing how data flows between agents."""
        enrichment_chain = []
        
        # SpaceX agent enriches with launch data
        if "spacex_data" in results:
            spacex_data = results["spacex_data"]
            enrichment_chain.append({
                "agent": "spacex",
                "input": "user_goal",
                "output": "launch_data_with_location",
                "enrichment_type": "data_acquisition",
                "data_size": len(str(spacex_data)) if spacex_data else 0
            })
        
        # Weather agent enriches with weather analysis
        if "weather_data" in results:
            weather_data = results["weather_data"]
            enrichment_chain.append({
                "agent": "weather",
                "input": "launch_location_from_spacex",
                "output": "weather_analysis_with_launch_suitability",
                "enrichment_type": "contextual_analysis",
                "data_size": len(str(weather_data)) if weather_data else 0
            })
        
        # News agent enriches with sentiment analysis
        if "news_data" in results:
            news_data = results["news_data"]
            enrichment_chain.append({
                "agent": "news",
                "input": "mission_name_from_spacex",
                "output": "news_sentiment_with_delay_indicators",
                "enrichment_type": "sentiment_analysis",
                "data_size": len(str(news_data)) if news_data else 0
            })
        
        # Coordinator enriches with synthesis
        if "final_assessment" in results:
            final_assessment = results["final_assessment"]
            enrichment_chain.append({
                "agent": "coordinator",
                "input": "all_agent_outputs",
                "output": "synthesized_assessment_with_recommendations",
                "enrichment_type": "synthesis_and_decision",
                "data_size": len(str(final_assessment)) if final_assessment else 0
            })
        
        return enrichment_chain
    
    def _analyze_trajectory(self, results: Dict[str, Any], test_config: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the agent execution trajectory."""
        actual_order = results.get("trajectory_metadata", {}).get("execution_order", [])
        expected_order = test_config.get("expected_trajectory", [])
        
        # Check if trajectory matches expected order
        trajectory_match = actual_order == expected_order if expected_order else True
        
        # Analyze trajectory efficiency
        expected_agents = 5  # planner, spacex, weather, news, coordinator
        actual_agents = len(actual_order)
        efficiency = min(actual_agents / expected_agents, 1.0) if expected_agents > 0 else 0
        
        # Check for proper sequencing
        proper_sequence = True
        if "spacex" in actual_order and "weather" in actual_order:
            spacex_idx = actual_order.index("spacex")
            weather_idx = actual_order.index("weather")
            if weather_idx <= spacex_idx:  # Weather should come after SpaceX
                proper_sequence = False
        
        return {
            "trajectory_match": trajectory_match,
            "expected_order": expected_order,
            "actual_order": actual_order,
            "efficiency": efficiency,
            "proper_sequence": proper_sequence,
            "agents_executed": len(actual_order),
            "trajectory_score": (int(trajectory_match) + efficiency + int(proper_sequence)) / 3
        }
    
    def _analyze_data_enrichment(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the data enrichment process."""
        enrichment_chain = results.get("trajectory_metadata", {}).get("enrichment_chain", [])
        
        # Check enrichment quality
        enrichment_types = [step["enrichment_type"] for step in enrichment_chain]
        expected_types = ["data_acquisition", "contextual_analysis", "sentiment_analysis", "synthesis_and_decision"]
        
        type_coverage = len(set(enrichment_types) & set(expected_types)) / len(expected_types)
        
        # Check data growth through enrichment
        data_sizes = [step["data_size"] for step in enrichment_chain]
        data_growth = len([i for i in range(1, len(data_sizes)) if data_sizes[i] > data_sizes[i-1]]) / max(len(data_sizes) - 1, 1)
        
        # Check for proper data dependencies
        dependency_satisfaction = self._check_dependency_satisfaction(results)
        
        return {
            "enrichment_chain_length": len(enrichment_chain),
            "enrichment_type_coverage": type_coverage,
            "data_growth_trend": data_growth,
            "dependency_satisfaction": dependency_satisfaction,
            "enrichment_details": enrichment_chain,
            "enrichment_score": (type_coverage + data_growth + dependency_satisfaction) / 3
        }
    
    def _analyze_dependencies(self, results: Dict[str, Any], test_config: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze agent dependency resolution."""
        actual_dependencies = results.get("trajectory_metadata", {}).get("data_dependencies", {})
        expected_dependencies = test_config.get("dependency_checks", [])
        
        dependency_scores = []
        
        for check in expected_dependencies:
            if len(check) == 3:  # (agent, "depends_on", dependency)
                agent, relation, dependency = check
                if relation == "depends_on":
                    if agent in actual_dependencies:
                        if isinstance(dependency, list):
                            satisfied = all(dep in actual_dependencies[agent] for dep in dependency)
                        else:
                            satisfied = dependency in actual_dependencies[agent]
                        dependency_scores.append(int(satisfied))
                    else:
                        dependency_scores.append(0)
        
        dependency_satisfaction = sum(dependency_scores) / len(dependency_scores) if dependency_scores else 1.0
        
        return {
            "expected_dependencies": expected_dependencies,
            "actual_dependencies": actual_dependencies,
            "dependency_satisfaction": dependency_satisfaction,
            "dependency_violations": self._find_dependency_violations(actual_dependencies),
            "dependency_score": dependency_satisfaction
        }
    
    def _check_dependency_satisfaction(self, results: Dict[str, Any]) -> float:
        """Check if data dependencies are properly satisfied."""
        # Check if weather agent got location data from SpaceX
        weather_has_location = False
        if "weather_data" in results:
            weather_data = results["weather_data"]
            if isinstance(weather_data, dict) and "location" in weather_data:
                weather_has_location = True
        
        # Check if news agent got mission data from SpaceX
        news_has_mission = False
        if "news_data" in results:
            news_data = results["news_data"]
            if isinstance(news_data, dict) and "mission_name" in news_data:
                news_has_mission = True
        
        # Check if coordinator got data from all agents
        coordinator_has_all_data = False
        if "final_assessment" in results:
            final_assessment = results["final_assessment"]
            if isinstance(final_assessment, dict) and "data_synthesis" in final_assessment:
                coordinator_has_all_data = True
        
        satisfied_dependencies = sum([weather_has_location, news_has_mission, coordinator_has_all_data])
        return satisfied_dependencies / 3
    
    def _find_dependency_violations(self, dependencies: Dict[str, List[str]]) -> List[str]:
        """Find any dependency violations in the execution."""
        violations = []
        
        # Check for circular dependencies
        for agent, deps in dependencies.items():
            if agent in deps:
                violations.append(f"Circular dependency: {agent} depends on itself")
        
        # Check for missing critical dependencies
        if "coordinator" in dependencies:
            coordinator_deps = dependencies["coordinator"]
            if "spacex" not in coordinator_deps:
                violations.append("Coordinator missing SpaceX dependency")
        
        return violations
    
    def _calculate_trajectory_score(self, trajectory_analysis: Dict[str, Any], 
                                  enrichment_analysis: Dict[str, Any], 
                                  dependency_analysis: Dict[str, Any]) -> float:
        """Calculate overall trajectory score."""
        trajectory_score = trajectory_analysis.get("trajectory_score", 0)
        enrichment_score = enrichment_analysis.get("enrichment_score", 0)
        dependency_score = dependency_analysis.get("dependency_score", 0)
        
        return (trajectory_score + enrichment_score + dependency_score) / 3
    
    async def run_full_evaluation(self) -> Dict[str, Any]:
        """Run evaluation on all trajectory tests."""
        print("🔄 Starting Agent Trajectory Evaluation")
        print("=" * 80)
        
        evaluations = []
        total_score = 0
        
        for test_config in self.trajectory_tests:
            evaluation = await self.evaluate_agent_trajectory(test_config)
            evaluations.append(evaluation)
            total_score += evaluation.get("overall_score", 0)
        
        average_score = total_score / len(self.trajectory_tests)
        
        summary = {
            "evaluation_type": "Agent Trajectory",
            "total_tests": len(self.trajectory_tests),
            "average_score": average_score,
            "individual_evaluations": evaluations,
            "overall_grade": "A" if average_score >= 0.9 else "B" if average_score >= 0.7 else "C" if average_score >= 0.5 else "D",
            "trajectory_insights": self._generate_trajectory_insights(evaluations)
        }
        
        print(f"\n🏆 TRAJECTORY EVALUATION SUMMARY")
        print(f"Average Score: {average_score:.2%}")
        print(f"Overall Grade: {summary['overall_grade']}")
        
        return summary
    
    def _generate_trajectory_insights(self, evaluations: List[Dict[str, Any]]) -> List[str]:
        """Generate insights about agent trajectory performance."""
        insights = []
        
        # Analyze common patterns
        trajectory_scores = [e.get("trajectory_analysis", {}).get("trajectory_score", 0) for e in evaluations]
        enrichment_scores = [e.get("enrichment_analysis", {}).get("enrichment_score", 0) for e in evaluations]
        dependency_scores = [e.get("dependency_analysis", {}).get("dependency_score", 0) for e in evaluations]
        
        avg_trajectory = sum(trajectory_scores) / len(trajectory_scores)
        avg_enrichment = sum(enrichment_scores) / len(enrichment_scores)
        avg_dependency = sum(dependency_scores) / len(dependency_scores)
        
        if avg_trajectory < 0.7:
            insights.append("Agent execution order needs optimization")
        
        if avg_enrichment < 0.7:
            insights.append("Data enrichment process could be improved")
        
        if avg_dependency < 0.7:
            insights.append("Agent dependency resolution needs attention")
        
        # Check for iteration patterns
        iterations_used = [e.get("execution_metadata", {}).get("iterations_used", 0) for e in evaluations]
        if any(i > 1 for i in iterations_used):
            insights.append("System successfully uses iterative refinement when needed")
        
        return insights


async def main():
    """Run the agent trajectory evaluation."""
    if not settings.validate():
        print("❌ Configuration validation failed. Please check your .env file.")
        return
    
    evaluator = AgentTrajectoryEvaluator()
    results = await evaluator.run_full_evaluation()
    
    # Save results
    with open("agent_trajectory_evaluation.json", "w") as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📄 Detailed results saved to agent_trajectory_evaluation.json")


if __name__ == "__main__":
    asyncio.run(main())
