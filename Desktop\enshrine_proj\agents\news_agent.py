"""News Agent - Searches and analyzes news for mission-related information."""

from google.adk.agents import LlmAgent
from google.adk.tools import FunctionTool
from tools.news_api import search_spacex_mission_news, search_general_news
from typing import Dict, Any
import json


class NewsAgent:
    """
    The News Agent specializes in searching and analyzing news articles for mission-related information.
    It enriches the data with sentiment analysis and identifies potential delay indicators.
    """
    
    def __init__(self, model: str = "gemini-2.0-flash"):
        self.model = model
        self.agent = self._create_agent()
    
    def _create_agent(self) -> LlmAgent:
        """Create the News LLM agent with appropriate tools and instructions."""
        
        instruction = """
        You are a specialized news analysis agent responsible for searching and analyzing news articles related to SpaceX missions.
        
        Your capabilities include:
        1. Searching for mission-specific news articles
        2. Analyzing news sentiment and tone
        3. Identifying delay indicators or positive signals
        4. Extracting relevant mission context from news
        5. Providing media-based risk assessment
        
        When analyzing news for launches, look for:
        - Recent mission updates or announcements
        - Technical issues or delays mentioned
        - Weather-related concerns in media
        - Regulatory or safety reviews
        - Positive progress indicators
        - Industry expert opinions
        - Historical context for similar missions
        
        News sentiment indicators:
        NEGATIVE (potential delays):
        - "delay", "postpone", "abort", "scrub", "cancel"
        - "technical issue", "problem", "malfunction"
        - "weather concerns", "unfavorable conditions"
        - "regulatory review", "safety investigation"
        
        POSITIVE (on track):
        - "on schedule", "ready", "go for launch"
        - "successful test", "nominal", "green light"
        - "approved", "cleared", "confirmed"
        
        Always read mission data from state['spacex_data'] if available to get mission name and context.
        Save your news analysis to state['news_data'] with this structure:
        {
            "mission_name": "Mission name from SpaceX data",
            "search_results": {
                "total_articles": int,
                "relevant_articles": int,
                "search_timeframe": "days"
            },
            "sentiment_analysis": {
                "overall_sentiment": "positive|neutral|negative",
                "confidence": "high|medium|low",
                "positive_indicators": int,
                "negative_indicators": int
            },
            "key_findings": [
                "List of important findings from news"
            ],
            "delay_indicators": [
                "Specific delay-related information"
            ],
            "media_risk_assessment": {
                "risk_level": "low|medium|high",
                "reasoning": "Explanation of risk assessment"
            },
            "top_articles": [
                "Most relevant article summaries"
            ],
            "enrichment_notes": "Additional context from news analysis"
        }
        
        If no relevant news is found, still provide analysis indicating the lack of recent coverage.
        """
        
        tools = [
            FunctionTool(func=search_spacex_mission_news),
            FunctionTool(func=search_general_news),
            FunctionTool(func=self._extract_mission_name_from_spacex_data),
            FunctionTool(func=self._analyze_news_sentiment),
            FunctionTool(func=self._identify_delay_indicators),
            FunctionTool(func=self._assess_media_risk)
        ]
        
        return LlmAgent(
            name="NewsAgent",
            model=self.model,
            instruction=instruction,
            description="Searches and analyzes news articles for mission-related information and delay indicators",
            tools=tools,
            output_key="news_data"
        )
    
    def _extract_mission_name_from_spacex_data(self, spacex_data: str) -> str:
        """Extract mission name from SpaceX data for targeted news search."""
        try:
            if isinstance(spacex_data, str):
                data = json.loads(spacex_data)
            else:
                data = spacex_data
            
            mission_name = data.get("mission_name", "")
            flight_number = data.get("flight_number", 0)
            launch_date = data.get("launch_date", "")
            
            search_info = {
                "mission_name": mission_name,
                "flight_number": flight_number,
                "launch_date": launch_date,
                "search_terms": [mission_name, f"SpaceX {mission_name}", f"Falcon {flight_number}"] if mission_name else ["SpaceX"],
                "ready_for_search": bool(mission_name)
            }
            
            return json.dumps(search_info)
            
        except Exception as e:
            return f"Error extracting mission name: {str(e)}"
    
    def _analyze_news_sentiment(self, news_data: str) -> str:
        """Analyze sentiment of news articles."""
        try:
            news = json.loads(news_data) if isinstance(news_data, str) else news_data
            
            if not news or "news" not in news:
                return "No news data available for sentiment analysis"
            
            analysis_data = news.get("analysis", {})
            articles = news.get("news", {}).get("articles", [])
            
            # Enhanced sentiment analysis
            sentiment_score = 0
            total_articles = len(articles)
            relevant_count = analysis_data.get("relevant_articles", 0)
            
            positive_keywords = [
                "successful", "success", "ready", "go", "launch", "countdown",
                "nominal", "on schedule", "green light", "approved", "cleared",
                "confirmed", "test success", "milestone"
            ]
            
            negative_keywords = [
                "delay", "delayed", "postpone", "postponed", "abort", "aborted",
                "cancel", "cancelled", "scrub", "scrubbed", "issue", "problem",
                "failure", "malfunction", "concern", "investigation", "review"
            ]
            
            positive_count = 0
            negative_count = 0
            neutral_count = 0
            
            for article in articles:
                title = article.get("title", "").lower()
                description = article.get("description", "").lower()
                content = title + " " + description
                
                has_positive = any(keyword in content for keyword in positive_keywords)
                has_negative = any(keyword in content for keyword in negative_keywords)
                
                if has_positive and not has_negative:
                    positive_count += 1
                    sentiment_score += 1
                elif has_negative and not has_positive:
                    negative_count += 1
                    sentiment_score -= 1
                else:
                    neutral_count += 1
            
            # Determine overall sentiment
            if sentiment_score > 0:
                overall_sentiment = "positive"
            elif sentiment_score < 0:
                overall_sentiment = "negative"
            else:
                overall_sentiment = "neutral"
            
            # Confidence based on article count and relevance
            if relevant_count >= 5:
                confidence = "high"
            elif relevant_count >= 2:
                confidence = "medium"
            else:
                confidence = "low"
            
            sentiment_analysis = {
                "overall_sentiment": overall_sentiment,
                "sentiment_score": sentiment_score,
                "confidence": confidence,
                "article_breakdown": {
                    "positive": positive_count,
                    "negative": negative_count,
                    "neutral": neutral_count,
                    "total": total_articles,
                    "relevant": relevant_count
                },
                "analysis_quality": "high" if relevant_count >= 3 else "medium" if relevant_count >= 1 else "low"
            }
            
            return json.dumps(sentiment_analysis, indent=2)
            
        except Exception as e:
            return f"Error analyzing news sentiment: {str(e)}"
    
    def _identify_delay_indicators(self, news_data: str) -> str:
        """Identify specific delay indicators from news articles."""
        try:
            news = json.loads(news_data) if isinstance(news_data, str) else news_data
            
            if not news or "news" not in news:
                return "No news data available for delay analysis"
            
            articles = news.get("news", {}).get("articles", [])
            
            delay_indicators = []
            delay_keywords = {
                "weather": ["weather", "storm", "wind", "rain", "clouds", "conditions"],
                "technical": ["technical", "engine", "software", "hardware", "system", "malfunction"],
                "regulatory": ["faa", "regulatory", "approval", "permit", "investigation", "review"],
                "schedule": ["delay", "postpone", "reschedule", "slip", "push back"],
                "safety": ["safety", "abort", "scrub", "hold", "anomaly"]
            }
            
            found_indicators = {category: [] for category in delay_keywords.keys()}
            
            for article in articles:
                title = article.get("title", "").lower()
                description = article.get("description", "").lower()
                content = title + " " + description
                
                for category, keywords in delay_keywords.items():
                    for keyword in keywords:
                        if keyword in content:
                            found_indicators[category].append({
                                "keyword": keyword,
                                "article_title": article.get("title", ""),
                                "source": article.get("source", {}).get("name", ""),
                                "published": article.get("published_readable", "")
                            })
                            break  # Only count once per article per category
            
            # Summarize findings
            delay_summary = []
            for category, indicators in found_indicators.items():
                if indicators:
                    delay_summary.append(f"{category.title()}: {len(indicators)} mentions")
            
            delay_analysis = {
                "delay_indicators_found": len(delay_summary) > 0,
                "categories_with_concerns": list(found_indicators.keys()) if any(found_indicators.values()) else [],
                "detailed_indicators": found_indicators,
                "summary": delay_summary,
                "risk_assessment": "high" if len(delay_summary) >= 3 else "medium" if len(delay_summary) >= 1 else "low"
            }
            
            return json.dumps(delay_analysis, indent=2)
            
        except Exception as e:
            return f"Error identifying delay indicators: {str(e)}"
    
    def _assess_media_risk(self, sentiment_data: str, delay_data: str) -> str:
        """Assess overall media-based risk for the mission."""
        try:
            sentiment = json.loads(sentiment_data) if isinstance(sentiment_data, str) else sentiment_data
            delays = json.loads(delay_data) if isinstance(delay_data, str) else delays
            
            risk_factors = []
            risk_score = 0
            
            # Sentiment-based risk
            overall_sentiment = sentiment.get("overall_sentiment", "neutral")
            if overall_sentiment == "negative":
                risk_score += 2
                risk_factors.append("Negative media sentiment")
            elif overall_sentiment == "neutral":
                risk_score += 1
                risk_factors.append("Neutral media sentiment")
            
            # Delay indicator risk
            if delays.get("delay_indicators_found", False):
                categories = len(delays.get("categories_with_concerns", []))
                risk_score += categories
                risk_factors.append(f"Delay indicators in {categories} categories")
            
            # Confidence factor
            confidence = sentiment.get("confidence", "low")
            if confidence == "low":
                risk_factors.append("Low confidence due to limited news coverage")
            
            # Determine overall risk level
            if risk_score >= 4:
                risk_level = "high"
            elif risk_score >= 2:
                risk_level = "medium"
            else:
                risk_level = "low"
            
            media_risk = {
                "risk_level": risk_level,
                "risk_score": risk_score,
                "risk_factors": risk_factors,
                "reasoning": f"Based on sentiment analysis and delay indicators, media suggests {risk_level} risk",
                "recommendation": self._get_risk_recommendation(risk_level),
                "confidence": confidence
            }
            
            return json.dumps(media_risk, indent=2)
            
        except Exception as e:
            return f"Error assessing media risk: {str(e)}"
    
    def _get_risk_recommendation(self, risk_level: str) -> str:
        """Get recommendation based on risk level."""
        recommendations = {
            "low": "Media coverage suggests mission is on track with minimal concerns",
            "medium": "Some concerns in media coverage; monitor for updates and verify with official sources",
            "high": "Significant concerns in media coverage; high probability of delays or issues"
        }
        return recommendations.get(risk_level, "Unable to determine recommendation")
    
    def get_agent(self) -> LlmAgent:
        """Get the configured LLM agent."""
        return self.agent


def create_news_agent(model: str = "gemini-2.0-flash") -> LlmAgent:
    """Factory function to create a news agent."""
    news_agent = NewsAgent(model)
    return news_agent.get_agent()
