"""OpenWeatherMap API integration tool."""

import asyncio
import httpx
from typing import Dict, List, Optional, Any, Tuple
from config.settings import settings


class WeatherAPI:
    """OpenWeatherMap API client for fetching weather data."""
    
    def __init__(self):
        self.base_url = settings.OPENWEATHER_API_URL
        self.api_key = settings.OPENWEATHER_API_KEY
        self.timeout = settings.TIMEOUT_SECONDS
    
    async def get_current_weather(self, lat: float, lon: float) -> Optional[Dict[str, Any]]:
        """Get current weather for given coordinates."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                params = {
                    "lat": lat,
                    "lon": lon,
                    "appid": self.api_key,
                    "units": "metric"
                }
                response = await client.get(f"{self.base_url}/weather", params=params)
                response.raise_for_status()
                return response.json()
        except Exception as e:
            print(f"Error fetching current weather: {e}")
            return None
    
    async def get_weather_forecast(self, lat: float, lon: float, days: int = 5) -> Optional[Dict[str, Any]]:
        """Get weather forecast for given coordinates."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                params = {
                    "lat": lat,
                    "lon": lon,
                    "appid": self.api_key,
                    "units": "metric",
                    "cnt": min(days * 8, 40)  # API returns 3-hour intervals, max 40 entries
                }
                response = await client.get(f"{self.base_url}/forecast", params=params)
                response.raise_for_status()
                return response.json()
        except Exception as e:
            print(f"Error fetching weather forecast: {e}")
            return None
    
    async def get_weather_by_city(self, city: str, country_code: str = "") -> Optional[Dict[str, Any]]:
        """Get current weather by city name."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                location = f"{city},{country_code}" if country_code else city
                params = {
                    "q": location,
                    "appid": self.api_key,
                    "units": "metric"
                }
                response = await client.get(f"{self.base_url}/weather", params=params)
                response.raise_for_status()
                return response.json()
        except Exception as e:
            print(f"Error fetching weather by city: {e}")
            return None
    
    def format_weather_data(self, weather_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format weather data for easier consumption by agents."""
        if not weather_data:
            return {}
        
        main = weather_data.get("main", {})
        weather = weather_data.get("weather", [{}])[0]
        wind = weather_data.get("wind", {})
        clouds = weather_data.get("clouds", {})
        
        formatted = {
            "location": {
                "name": weather_data.get("name", ""),
                "country": weather_data.get("sys", {}).get("country", ""),
                "coordinates": {
                    "lat": weather_data.get("coord", {}).get("lat"),
                    "lon": weather_data.get("coord", {}).get("lon")
                }
            },
            "current": {
                "temperature": main.get("temp"),
                "feels_like": main.get("feels_like"),
                "humidity": main.get("humidity"),
                "pressure": main.get("pressure"),
                "description": weather.get("description", ""),
                "main": weather.get("main", ""),
                "visibility": weather_data.get("visibility"),
                "uv_index": weather_data.get("uvi")
            },
            "wind": {
                "speed": wind.get("speed"),
                "direction": wind.get("deg"),
                "gust": wind.get("gust")
            },
            "clouds": {
                "coverage": clouds.get("all")
            },
            "precipitation": {
                "rain_1h": weather_data.get("rain", {}).get("1h", 0),
                "snow_1h": weather_data.get("snow", {}).get("1h", 0)
            },
            "timestamp": weather_data.get("dt"),
            "sunrise": weather_data.get("sys", {}).get("sunrise"),
            "sunset": weather_data.get("sys", {}).get("sunset")
        }
        
        return formatted
    
    def format_forecast_data(self, forecast_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format forecast data for easier consumption by agents."""
        if not forecast_data:
            return {}
        
        city = forecast_data.get("city", {})
        forecasts = []
        
        for item in forecast_data.get("list", []):
            main = item.get("main", {})
            weather = item.get("weather", [{}])[0]
            wind = item.get("wind", {})
            
            forecast_item = {
                "datetime": item.get("dt_txt", ""),
                "timestamp": item.get("dt"),
                "temperature": main.get("temp"),
                "feels_like": main.get("feels_like"),
                "humidity": main.get("humidity"),
                "pressure": main.get("pressure"),
                "description": weather.get("description", ""),
                "main": weather.get("main", ""),
                "wind_speed": wind.get("speed"),
                "wind_direction": wind.get("deg"),
                "clouds": item.get("clouds", {}).get("all"),
                "precipitation_probability": item.get("pop", 0),
                "rain_3h": item.get("rain", {}).get("3h", 0),
                "snow_3h": item.get("snow", {}).get("3h", 0)
            }
            forecasts.append(forecast_item)
        
        formatted = {
            "location": {
                "name": city.get("name", ""),
                "country": city.get("country", ""),
                "coordinates": {
                    "lat": city.get("coord", {}).get("lat"),
                    "lon": city.get("coord", {}).get("lon")
                }
            },
            "forecast": forecasts
        }
        
        return formatted
    
    def analyze_launch_weather(self, weather_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze weather conditions for launch suitability."""
        if not weather_data:
            return {"suitable": False, "reason": "No weather data available"}
        
        current = weather_data.get("current", {})
        wind = weather_data.get("wind", {})
        precipitation = weather_data.get("precipitation", {})
        
        # Launch weather criteria (simplified)
        issues = []
        
        # Wind speed check (typical limit ~15 m/s for launches)
        wind_speed = wind.get("speed", 0)
        if wind_speed > 15:
            issues.append(f"High wind speed: {wind_speed} m/s")
        
        # Precipitation check
        rain = precipitation.get("rain_1h", 0)
        snow = precipitation.get("snow_1h", 0)
        if rain > 0 or snow > 0:
            issues.append(f"Precipitation detected: rain={rain}mm, snow={snow}mm")
        
        # Cloud coverage check
        clouds = weather_data.get("clouds", {}).get("coverage", 0)
        if clouds > 80:
            issues.append(f"Heavy cloud coverage: {clouds}%")
        
        # Visibility check
        visibility = current.get("visibility", 10000)
        if visibility < 5000:
            issues.append(f"Low visibility: {visibility}m")
        
        # Temperature extremes (very rough check)
        temp = current.get("temperature", 20)
        if temp < -10 or temp > 40:
            issues.append(f"Extreme temperature: {temp}°C")
        
        suitable = len(issues) == 0
        
        return {
            "suitable": suitable,
            "issues": issues,
            "weather_summary": {
                "temperature": temp,
                "wind_speed": wind_speed,
                "description": current.get("description", ""),
                "clouds": clouds,
                "precipitation": rain + snow
            }
        }


# Function tools for ADK integration
async def fetch_weather_for_location(latitude: float, longitude: float) -> str:
    """Fetch current weather for given coordinates."""
    api = WeatherAPI()
    weather_data = await api.get_current_weather(latitude, longitude)
    
    if not weather_data:
        return "Failed to fetch weather data"
    
    formatted_data = api.format_weather_data(weather_data)
    launch_analysis = api.analyze_launch_weather(formatted_data)
    
    result = {
        "weather": formatted_data,
        "launch_analysis": launch_analysis
    }
    
    return str(result)


async def fetch_weather_forecast_for_location(latitude: float, longitude: float, days: int = 3) -> str:
    """Fetch weather forecast for given coordinates."""
    api = WeatherAPI()
    forecast_data = await api.get_weather_forecast(latitude, longitude, days)
    
    if not forecast_data:
        return "Failed to fetch weather forecast"
    
    formatted_data = api.format_forecast_data(forecast_data)
    return str(formatted_data)
