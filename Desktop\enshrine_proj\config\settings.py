"""Configuration settings for the multi-agent system."""

import os
from typing import Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Settings:
    """Application settings loaded from environment variables."""
    
    # Google Cloud / Gemini API
    GOOGLE_API_KEY: str = os.getenv("GOOGLE_API_KEY", "")
    GOOGLE_CLOUD_PROJECT: str = os.getenv("GOOGLE_CLOUD_PROJECT", "")
    
    # External APIs
    OPENWEATHER_API_KEY: str = os.getenv("OPENWEATHER_API_KEY", "")
    NEWS_API_KEY: str = os.getenv("NEWS_API_KEY", "")
    COINGECKO_API_KEY: str = os.getenv("COINGECKO_API_KEY", "")
    
    # Application settings
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    MAX_ITERATIONS: int = int(os.getenv("MAX_ITERATIONS", "3"))
    TIMEOUT_SECONDS: int = int(os.getenv("TIMEOUT_SECONDS", "30"))
    
    # API URLs
    SPACEX_API_URL: str = "https://api.spacexdata.com/v4"
    OPENWEATHER_API_URL: str = "https://api.openweathermap.org/data/2.5"
    NEWS_API_URL: str = "https://newsapi.org/v2"
    COINGECKO_API_URL: str = "https://api.coingecko.com/api/v3"
    
    def validate(self) -> bool:
        """Validate that required settings are present."""
        required_keys = [
            "GOOGLE_API_KEY",
            "OPENWEATHER_API_KEY", 
            "NEWS_API_KEY"
        ]
        
        missing_keys = []
        for key in required_keys:
            if not getattr(self, key):
                missing_keys.append(key)
        
        if missing_keys:
            print(f"Missing required environment variables: {', '.join(missing_keys)}")
            return False
        
        return True

# Global settings instance
settings = Settings()
